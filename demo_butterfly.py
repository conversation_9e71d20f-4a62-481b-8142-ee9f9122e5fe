#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QProgressBar, QLabel, QFileDialog, QMessageBox
from PyQt5.QtCore import pyqtSlot

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from Codebase_Butterfly import ButterflyProcessor

class ButterflyDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🦋 蝴蝶翻飞-视频号处理器演示")
        self.setGeometry(100, 100, 700, 500)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🦋 蝴蝶翻飞-视频号处理器演示")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel("""
功能说明：
1. 把素材(A视频)和实拍(B视频)的时长统一
2. 把实拍的音频关闭，把素材的音频分离出来
3. 把实拍和素材的比例都调成1:1
4. 给素材添加蒙版(矩形)，羽化参数调整至12
5. 把素材和实拍添加成复合素材
6. 等比缩放关闭，把比例调整成9:16，缩放高度调至182-185%
7. 导出为：A视频文件名_sph.mp4
        """)
        info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 文件选择区域
        file_layout = QVBoxLayout()
        
        # A视频选择
        self.a_video_label = QLabel("A视频(素材): 未选择")
        file_layout.addWidget(self.a_video_label)
        
        a_video_btn = QPushButton("选择A视频(素材)")
        a_video_btn.clicked.connect(self.select_a_video)
        file_layout.addWidget(a_video_btn)
        
        # B视频选择
        self.b_video_label = QLabel("B视频(实拍): 未选择")
        file_layout.addWidget(self.b_video_label)
        
        b_video_btn = QPushButton("选择B视频(实拍)")
        b_video_btn.clicked.connect(self.select_b_video)
        file_layout.addWidget(b_video_btn)
        
        # 输出目录选择
        self.output_label = QLabel("输出目录: 未选择")
        file_layout.addWidget(self.output_label)
        
        output_btn = QPushButton("选择输出目录")
        output_btn.clicked.connect(self.select_output_dir)
        file_layout.addWidget(output_btn)
        
        layout.addLayout(file_layout)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #333;")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 控制按钮
        button_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("🚀 开始处理")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止处理")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 10px;")
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化变量
        self.a_video_path = None
        self.b_video_path = None
        self.output_dir = None
        self.worker = None
        
        # FFmpeg路径
        self.ffmpeg_path = os.path.join(os.path.dirname(__file__), "bin", "ffmpeg.exe")
        if not os.path.exists(self.ffmpeg_path):
            self.ffmpeg_path = "ffmpeg"
    
    def select_a_video(self):
        """选择A视频(素材)"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择A视频(素材)", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.m4v *.3gp *.webm)"
        )
        if file_path:
            self.a_video_path = file_path
            self.a_video_label.setText(f"A视频(素材): {os.path.basename(file_path)}")
    
    def select_b_video(self):
        """选择B视频(实拍)"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择B视频(实拍)", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.m4v *.3gp *.webm)"
        )
        if file_path:
            self.b_video_path = file_path
            self.b_video_label.setText(f"B视频(实拍): {os.path.basename(file_path)}")
    
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_label.setText(f"输出目录: {dir_path}")
    
    def start_processing(self):
        """开始处理"""
        # 检查输入
        if not self.a_video_path:
            QMessageBox.warning(self, "错误", "请先选择A视频(素材)")
            return
        
        if not self.b_video_path:
            QMessageBox.warning(self, "错误", "请先选择B视频(实拍)")
            return
        
        if not self.output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录")
            return
        
        try:
            # 创建蝴蝶翻飞处理器
            self.worker = ButterflyProcessor(
                self.ffmpeg_path, 
                [self.a_video_path], 
                [self.b_video_path],
                self.output_dir, 
                "蝴蝶翻飞-视频号演示", 
                False
            )
            
            # 连接信号
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.process_finished.connect(self.on_process_finished)
            
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.status_label.setText("开始处理...")
            self.progress_bar.setValue(0)
            
            # 启动处理
            self.worker.start()
            print("🦋 蝴蝶翻飞处理器已启动")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动处理失败: {str(e)}")
            print(f"启动处理失败: {e}")
    
    def stop_processing(self):
        """停止处理"""
        if self.worker:
            self.worker.cancel()
            self.worker = None
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("已停止")
    
    @pyqtSlot(int, str)
    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        print(f"进度: {progress}% - {message}")
    
    @pyqtSlot(bool, str)
    def on_process_finished(self, success, message):
        """处理完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("✅ 处理完成！")
            QMessageBox.information(self, "成功", f"处理成功！\n\n{message}")
            print(f"✅ 处理成功: {message}")
        else:
            self.status_label.setText("❌ 处理失败")
            QMessageBox.critical(self, "失败", f"处理失败！\n\n{message}")
            print(f"❌ 处理失败: {message}")
        
        self.worker = None

def main():
    app = QApplication(sys.argv)
    window = ButterflyDemo()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
