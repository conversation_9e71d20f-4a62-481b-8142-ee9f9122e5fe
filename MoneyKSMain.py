import sys
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
import os
import subprocess

import cv2
import random
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QPushButton, QFileDialog,
                           QFrame, QProgressBar, QMessageBox, QCheckBox, QListWidget, QListWidgetItem, QMenu, QAction, QLineEdit, QComboBox)
from PyQt5.QtCore import Qt, QTimer, QUrl
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from loginks import LoginWindow
import requests
from datetime import datetime
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad, pad
import tempfile
import shutil
import urllib.request
# 导入星火KS处理器
from MoneyKS import FireProcessor
# 导入鱿鱼游戏处理器
from Codebase_Squid import SquidProcessor
# 导入风火轮处理器
from Codebase_Fenghuo_nonvenc import FenghuoNonNvencProcessor
# 导入风火轮二代处理器
from Codebase_Fenghuo_v2 import FenghuoV2Processor
# 导入风火轮二代N卡处理器
from Codebase_Fenghuo_v2_nvenc import FenghuoV2NvencProcessor
# 导入夏日狂欢（KS）处理器
from Codebase_KuangSha import KuangShaProcessor
# 导入鸭嘴兽（KS）处理器
from Codebase_Platypus1 import PlatypusProcessor


class MainWindow(QMainWindow):
    def __init__(self, expiry_time_str, card_key, mute=False, aes_key="", aes_iv=""):
        super().__init__()
        self.worker = None
        self.is_processing = False
        self.expiry_time_str = expiry_time_str
        self.card_key = card_key
        self.mute = mute
        self.aes_key = aes_key
        self.aes_iv = aes_iv
        self.expiry_time = None
        self.ffmpeg_path = None
        self.mkvmerge_path = None
        
        # 打印AES密钥信息
        if self.aes_key and self.aes_iv:
            print(f"AES1: {self.aes_key}")
            print(f"AES2: {self.aes_iv}")
        
        try:
            self.expiry_time = datetime.strptime(expiry_time_str, "%Y-%m-%d %H:%M:%S")
        except Exception:
            self.expiry_time = None

        # 创建统一的播放器
        self._player = QMediaPlayer()
        mp3_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "dazhanhongtu.MP3")
        self._player.setMedia(QMediaContent(QUrl.fromLocalFile(mp3_path)))
        self._player.setVolume(100)
        # 设置循环播放
        self._player.mediaStatusChanged.connect(self.handle_media_status)

        # 初始化变量（在initUI之前）
        self.image_folder_path = ""
        self.video_files = []
        self.b_video_folder_path = ""  # B视频文件夹路径
        self.current_function = "夏日狂欢（KS）"  # 当前选择的功能，默认为夏日狂欢

        # 记录实际使用过的B素材
        self.used_b_materials = set()

        # AI生成图片相关
        self.ai_generated_temp_dir = None  # AI生成图片的临时目录
        self.ai_temp_folder = None  # AI生成图片的临时文件夹

        self.initUI()

        # 自动检测bin目录下的关键组件
        self.auto_detect_bin_components()

        # 在initUI之后启动播放
        if not self.mute:
            QTimer.singleShot(100, self._player.play)  # 延迟100ms启动播放

        # 启动到期时间定时检查（每5秒检查一次）
        self.expiry_check_timer = QTimer(self)
        self.expiry_check_timer.timeout.connect(self.check_expiry_time)
        self.expiry_check_timer.start(5000)

        self.hot_typing_timer = QTimer(self)
        self.hot_typing_timer.timeout.connect(self._hot_typing_step)
        self.hot_typing_full_text = ""
        self.hot_typing_index = 0
        self.hot_typing_delay = 0
        self.hot_typing_base = "🔈："
        self.hot_refresh_timer = QTimer(self)
        self.hot_refresh_timer.setSingleShot(True)
        self.hot_refresh_timer.timeout.connect(self.update_hot_value_with_typing)
        self.update_hot_value_with_typing()  # 启动首次动画

    def auto_detect_bin_components(self):
        """自动检测bin目录下的关键组件"""
        # 获取程序所在目录
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        print(f"程序所在目录: {exe_dir}")
        
        # 检查bin文件夹是否存在
        bin_dir = os.path.join(exe_dir, "bin")
        if not os.path.exists(bin_dir) or not os.path.isdir(bin_dir):
            print("未找到bin文件夹")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查bin文件夹中的ffmpeg.exe是否存在
        ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
        if not os.path.exists(ffmpeg_exe) or not os.path.isfile(ffmpeg_exe):
            print("未找到ffmpeg.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查bin文件夹中的mkvmerge.exe是否存在
        mkvmerge_exe = os.path.join(bin_dir, "mkvmerge.exe")
        if not os.path.exists(mkvmerge_exe) or not os.path.isfile(mkvmerge_exe):
            print("未找到mkvmerge.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查其他关键文件
        required_dlls = [
            "avcodec-62.dll",
            "avdevice-62.dll",
            "avfilter-11.dll",
            "avformat-62.dll",
            "avutil-60.dll",
            "swresample-6.dll",
            "swscale-9.dll"
        ]
        
        missing_files = []
        for file in required_dlls:
            file_path = os.path.join(bin_dir, file)
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                print(f"未找到关键文件: {file}")
                missing_files.append(file)
                
        if missing_files:
            print(f"缺少关键文件: {', '.join(missing_files)}")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 设置ffmpeg和mkvmerge路径
        self.ffmpeg_path = os.path.dirname(bin_dir)  # 指向bin的父目录
        self.mkvmerge_path = mkvmerge_exe  # 直接指向mkvmerge.exe
        print(f"自动检测到ffmpeg路径: {self.ffmpeg_path}")
        print(f"自动检测到mkvmerge路径: {self.mkvmerge_path}")
        
        # 验证ffmpeg是否可用
        try:
            result = subprocess.run(
                [ffmpeg_exe, "-version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("ffmpeg验证成功")
            else:
                print(f"ffmpeg验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"ffmpeg验证异常: {e}")
            QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
            sys.exit(1)
            
        # 验证mkvmerge是否可用
        try:
            result = subprocess.run(
                [mkvmerge_exe, "--version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("mkvmerge验证成功")
            else:
                print(f"mkvmerge验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"mkvmerge验证异常: {e}")
            QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
            sys.exit(1)

    def get_ffmpeg_executable(self):
        """获取FFMPEG可执行文件路径"""
        if self.ffmpeg_path:
            return os.path.join(self.ffmpeg_path, "bin", "ffmpeg.exe")
        return None

    def handle_media_status(self, status):
        # 当音乐播放结束时，如果勾选框是选中状态，则重新开始播放
        if status == QMediaPlayer.EndOfMedia and not self.mute and self.bgm_checkbox.isChecked():
            self._player.setPosition(0)
            self._player.play()

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle("疾风快手版-夏日狂欢（07.25鸭嘴兽更新） | 本软件开发用途仅供学习参考，请勿用于非法用途！")
        self.setMinimumSize(800, 600)

        hot_value = round(random.uniform(1100, 1300), 1)

        # === 创建中心控件与垂直布局 ===
        center_widget = QWidget()
        self.central_layout = QVBoxLayout(center_widget)
        self.central_layout.setContentsMargins(20, 20, 20, 20)
        self.central_layout.setSpacing(20)

        # 顶部通知标签，用于滚动广告文字
        self.notification_label = QLabel(f"🔈：疾风快手版-版本：ALL 最新爆单排行：{hot_value}万播放量！")
        self.notification_label.setFont(QFont("微软雅黑", 10))
        self.notification_label.setAlignment(Qt.AlignCenter)
        self.notification_label.setStyleSheet("""
            background-color: #f8f9fa;
            color: #e74c3c;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        """)
        self.central_layout.addWidget(self.notification_label)

        # 设置动态文字前缀
        self.hot_typing_base = "🔈：疾风快手版-风火轮"

        # 启动更新热度值的定时器
        self.typing_update_timer = QTimer()
        self.typing_update_timer.timeout.connect(self.update_hot_value_with_typing)
        self.typing_update_timer.start(10000)  # 每10秒更新一次

        # === 主功能栏（顶部工具栏） ===
        toolbar_layout = QHBoxLayout()

        # 左侧标题和Logo
        left_group = QHBoxLayout()
        title_label = QLabel("疾风快手版-风火轮")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setStyleSheet("color: #3498db;")
        left_group.addWidget(title_label)

        # 添加到期时间标签
        self.expiry_label = QLabel(f"到期时间：{self.expiry_time_str}")
        self.expiry_label.setFont(QFont("微软雅黑", 9))
        self.expiry_label.setStyleSheet("color: #222;")
        left_group.addWidget(self.expiry_label)

        # 添加BGM勾选框（如果不是静音状态）
        if not self.mute:
            self.bgm_checkbox = QCheckBox("大展宏图BGM")
            self.bgm_checkbox.setFont(QFont("微软雅黑", 9))
            self.bgm_checkbox.setStyleSheet("""
                QCheckBox {
                    color: #222;
                }
                QCheckBox::indicator {
                    width: 13px;
                    height: 13px;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #999;
                    background: white;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #3498db;
                    background: #3498db;
                }
            """)
            self.bgm_checkbox.setChecked(True)
            self.bgm_checkbox.stateChanged.connect(self.toggle_bgm)
            left_group.addWidget(self.bgm_checkbox)

        left_group.addStretch()

        toolbar_layout.addLayout(left_group)

        # 右侧功能选择
        right_group = QHBoxLayout()

        function_label = QLabel("功能选择：")
        function_label.setFont(QFont("微软雅黑", 10))
        function_label.setStyleSheet("color: #2980b9;")
        right_group.addWidget(function_label)

        self.function_combo = QComboBox()
        self.function_combo.addItems(["夏日狂欢（KS）", "风火轮（KS爆流）", "星火KS（优化版）", "风火轮二代（KS-CPU）", "风火轮二代（KS-N卡加速）", "鸭嘴兽（KS）25号最新通道"])
        self.function_combo.setCurrentText("夏日狂欢（KS）")
        self.function_combo.setFont(QFont("微软雅黑", 14))
        self.function_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 1px solid #3498db;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 150px;
                color: #2c3e50;
            }
            QComboBox:hover {
                border: 1px solid #2980b9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #3498db;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #3498db;
                selection-background-color: #e8f4fc;
                selection-color: #2c3e50;
                color: #2c3e50;
            }
        """)
        self.function_combo.currentTextChanged.connect(self.on_function_changed)
        right_group.addWidget(self.function_combo)

        toolbar_layout.addLayout(left_group)
        toolbar_layout.addStretch()
        toolbar_layout.addLayout(right_group)
        self.central_layout.addLayout(toolbar_layout)

        # 创建视频和图片选择区域
        selection_layout = QHBoxLayout()

        # 创建视频列表区域
        video_section = QVBoxLayout()
        video_label = QLabel("A视频列表（素材）")
        video_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        video_label.setStyleSheet("color: #2980b9; text-align: center;")
        video_label.setAlignment(Qt.AlignCenter)
        video_section.addWidget(video_label)

        # 创建视频列表控件
        self.video_list_widget = QListWidget()
        self.video_list_widget.setAcceptDrops(True)
        self.video_list_widget.setDragDropMode(QListWidget.DropOnly)
        self.video_list_widget.setStyleSheet("""
            QListWidget {
                background-color: #F8FAFC;
                border: 1px solid #BED6F6;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E1EBFA;
                color: #2c3e50;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #E8F4FC;
            }
        """)
        self.video_list_widget.setMinimumHeight(200)
        self.video_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.video_list_widget.customContextMenuRequested.connect(self.show_video_context_menu)
        video_section.addWidget(self.video_list_widget)

        # 视频操作按钮
        video_btn_layout = QHBoxLayout()
        self.add_video_btn = QPushButton("添加视频")
        self.add_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.add_video_btn.clicked.connect(self.add_videos)

        self.clear_video_btn = QPushButton("清空列表")
        self.clear_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.clear_video_btn.clicked.connect(self.clear_video_list)

        video_btn_layout.addWidget(self.add_video_btn)
        video_btn_layout.addWidget(self.clear_video_btn)
        video_btn_layout.addStretch()
        video_section.addLayout(video_btn_layout)

        # 创建B素材选择区域（支持图片文件夹和B视频列表）
        b_material_section = QVBoxLayout()
        self.b_material_label = QLabel("图片文件夹")
        self.b_material_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        self.b_material_label.setStyleSheet("color: #2980b9; text-align: center;")
        self.b_material_label.setAlignment(Qt.AlignCenter)
        b_material_section.addWidget(self.b_material_label)

        # 图片文件夹显示区域
        self.image_folder_frame = QFrame()
        self.image_folder_frame.setStyleSheet("""
            QFrame {
                background-color: #F8FAFC;
                border: 1px solid #BED6F6;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        self.image_folder_frame.setMinimumHeight(200)

        image_folder_layout = QVBoxLayout(self.image_folder_frame)

        self.image_folder_label = QLabel("未选择图片文件夹")
        self.image_folder_label.setAlignment(Qt.AlignCenter)
        self.image_folder_label.setStyleSheet("color: #7f8c8d; font-size: 14px;")
        image_folder_layout.addWidget(self.image_folder_label)

        self.image_count_label = QLabel("")
        self.image_count_label.setAlignment(Qt.AlignCenter)
        self.image_count_label.setStyleSheet("color: #3498db; font-size: 12px;")
        image_folder_layout.addWidget(self.image_count_label)

        b_material_section.addWidget(self.image_folder_frame)

        # B素材操作按钮区域
        b_material_btn_layout = QHBoxLayout()
        self.select_image_folder_btn = QPushButton("选择图片文件夹")
        self.select_image_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_image_folder_btn.clicked.connect(self.select_image_folder)

        # B视频选择按钮（初始隐藏）
        self.select_b_video_btn = QPushButton("选择B视频文件夹")
        self.select_b_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_b_video_btn.clicked.connect(self.select_b_video_folder)
        self.select_b_video_btn.hide()  # 初始隐藏

        b_material_btn_layout.addWidget(self.select_image_folder_btn)
        b_material_btn_layout.addWidget(self.select_b_video_btn)

        # AI生成图片勾选框
        self.ai_generate_images_checkbox = QCheckBox("无需素材，AI自动生成图片（内测）")
        self.ai_generate_images_checkbox.setFont(QFont("微软雅黑", 10))
        self.ai_generate_images_checkbox.setStyleSheet("""
            QCheckBox {
                color: #e67e22;
                spacing: 5px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #e67e22;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #e67e22;
                border-radius: 3px;
            }
        """)
        self.ai_generate_images_checkbox.setChecked(False)
        self.ai_generate_images_checkbox.setToolTip("勾选后将使用AI自动生成二次元图片，无需手动选择图片文件夹")
        self.ai_generate_images_checkbox.stateChanged.connect(self.on_ai_generate_checkbox_changed)

        b_material_btn_layout.addWidget(self.ai_generate_images_checkbox)
        b_material_btn_layout.addStretch()
        b_material_section.addLayout(b_material_btn_layout)

        # 添加到主布局
        selection_layout.addLayout(video_section)
        selection_layout.addLayout(b_material_section)
        self.central_layout.addLayout(selection_layout)

        # === 输出目录设置 ===
        output_section = QFrame()
        output_section.setFrameShape(QFrame.HLine)
        output_section.setFrameShadow(QFrame.Sunken)
        output_section.setStyleSheet("color: #bbb; border: 1px dashed #bbb; margin: 8px 0;")
        self.central_layout.addWidget(output_section)

        output_layout = QHBoxLayout()
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(10)

        output_label = QLabel("输出目录：")
        output_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        output_label.setStyleSheet("color: #2980b9;")
        output_layout.addWidget(output_label)

        # 输出路径输入框
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("默认导出到原视频所在目录")
        self.output_path_edit.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #BED6F6;
                border-radius: 4px;
                background: #F8FAFC;
                color: #2c3e50;
                font-size: 13px;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background: white;
            }
        """)
        output_layout.addWidget(self.output_path_edit)

        # 选择目录按钮
        self.select_output_btn = QPushButton("📂")
        self.select_output_btn.setFixedWidth(32)
        self.select_output_btn.setStyleSheet("""
            QPushButton {
                background-color: #E8F4FC;
                border: 1px solid #BED6F6;
                border-radius: 4px;
                padding: 5px;
                color: #2980b9;
            }
            QPushButton:hover {
                background-color: #D1E8F8;
                border: 1px solid #3498db;
            }
        """)
        self.select_output_btn.clicked.connect(self.select_output_directory)
        output_layout.addWidget(self.select_output_btn)

        self.central_layout.addLayout(output_layout)

        # === 处理选项设置 ===
        options_section = QFrame()
        options_section.setFrameShape(QFrame.HLine)
        options_section.setFrameShadow(QFrame.Sunken)
        options_section.setStyleSheet("color: #bbb; border: 1px dashed #bbb; margin: 8px 0;")
        self.central_layout.addWidget(options_section)

        options_layout = QHBoxLayout()
        options_layout.setContentsMargins(0, 0, 0, 0)
        options_layout.setSpacing(20)

        # 删除使用过的B素材勾选框
        self.delete_used_b_checkbox = QCheckBox("删除使用过的B素材")
        self.delete_used_b_checkbox.setFont(QFont("微软雅黑", 10))
        self.delete_used_b_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #bdc3c7;
                background: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #e74c3c;
                background: #e74c3c;
                border-radius: 3px;
            }
        """)
        self.delete_used_b_checkbox.setChecked(False)  # 默认不勾选
        self.delete_used_b_checkbox.setToolTip("勾选后，处理完成会自动删除参与处理的B素材文件（图片/视频）")

        options_layout.addWidget(self.delete_used_b_checkbox)
        options_layout.addStretch()

        self.central_layout.addLayout(options_layout)

        # 创建处理按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)

        # 修改按钮样式
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QPushButton#cancel {
                background-color: #e74c3c;
            }
            QPushButton#cancel:hover {
                background-color: #c0392b;
            }
            QPushButton#cancel:pressed {
                background-color: #a93226;
            }
        """

        self.yin_yang_btn = QPushButton("开始处理")
        self.yin_yang_btn.setStyleSheet(button_style)

        # 初始时按钮禁用
        self.yin_yang_btn.setEnabled(False)

        # 连接按钮信号
        self.yin_yang_btn.clicked.connect(self.process_yin_yang)

        # 添加按钮到布局
        button_layout.addStretch()
        button_layout.addWidget(self.yin_yang_btn)
        button_layout.addStretch()

        # 添加状态标签
        self.status_label = QLabel("请选择视频和图片文件夹")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

        # 添加到主布局
        self.central_layout.addLayout(button_layout)
        self.central_layout.addWidget(self.status_label)

        # 底部进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat('%p%')
        self.progress_bar.setStyleSheet('''
            QProgressBar {
                background-color: #E1EBFA;
                border: none;
                border-radius: 4px;
                text-align: center;
                color: #3498db;
                font-weight: bold;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 4px;
            }
        ''')
        self.central_layout.addWidget(self.progress_bar)

        # 设置中心控件
        self.setCentralWidget(center_widget)

        # 初始化时根据默认功能更新UI显示
        self.on_function_changed(self.current_function)

    def check_videos_and_update_buttons(self):
        """检查是否已选择视频和B素材，并相应地更新按钮状态"""
        video_count = self.video_list_widget.count()

        if self.current_function in ["星火KS（优化版）", "风火轮（KS爆流）", "风火轮二代（KS-CPU）", "风火轮二代（KS-N卡加速）", "鸭嘴兽（KS）25号最新通道"]:
            # 星火KS、风火轮、风火轮二代和鸭嘴兽模式：需要A视频和图片文件夹（或AI生成图片）
            has_b_material = (
                bool(self.image_folder_path and os.path.exists(self.image_folder_path)) or
                self.ai_generate_images_checkbox.isChecked()
            )
            b_material_name = "图片文件夹" if not self.ai_generate_images_checkbox.isChecked() else "AI生成图片"
        elif self.current_function == "夏日狂欢（KS）":
            # 夏日狂欢模式：需要A视频和B视频文件夹
            has_b_material = bool(self.b_video_folder_path and os.path.exists(self.b_video_folder_path))
            b_material_name = "B视频文件夹"
        else:
            has_b_material = False
            b_material_name = "B素材"

        if video_count > 0 and has_b_material:
            self.yin_yang_btn.setEnabled(True)
            self.status_label.setText(f"已选择 {video_count} 个A视频和{b_material_name}，可以进行处理")
            self.status_label.setStyleSheet("color: #27ae60; font-size: 12px;")
        else:
            self.yin_yang_btn.setEnabled(False)
            if video_count == 0 and not has_b_material:
                self.status_label.setText(f"请添加A视频和选择{b_material_name}")
            elif video_count == 0:
                self.status_label.setText("请添加A视频")
            else:
                self.status_label.setText(f"请选择{b_material_name}")
            self.status_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

    def add_videos(self):
        """添加视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.wmv *.flv *.mkv *.webm *.m4v *.3gp)"
        )

        if files:
            for file_path in files:
                if self.is_video_file(file_path):
                    # 获取视频信息
                    video_info = self.get_video_info(file_path)
                    if video_info:
                        # 创建列表项
                        item = QListWidgetItem()
                        filename = os.path.basename(file_path)
                        duration_str = self.format_duration(video_info['duration'])
                        item.setText(f"{filename} ({duration_str})")
                        item.setData(Qt.UserRole, video_info)
                        self.video_list_widget.addItem(item)

            self.check_videos_and_update_buttons()

    def clear_video_list(self):
        """清空视频列表"""
        self.video_list_widget.clear()
        self.check_videos_and_update_buttons()

    def show_video_context_menu(self, position):
        """显示视频列表右键菜单"""
        if self.video_list_widget.itemAt(position):
            menu = QMenu(self)

            delete_action = QAction("删除", self)
            delete_action.triggered.connect(self.delete_selected_video)
            menu.addAction(delete_action)

            menu.exec_(self.video_list_widget.mapToGlobal(position))

    def delete_selected_video(self):
        """删除选中的视频"""
        current_row = self.video_list_widget.currentRow()
        if current_row >= 0:
            self.video_list_widget.takeItem(current_row)
            self.check_videos_and_update_buttons()

    def on_function_changed(self, function_name):
        """功能切换处理"""
        self.current_function = function_name
        print(f"切换到功能: {function_name}")

        if function_name in ["星火KS（优化版）", "风火轮（KS爆流）", "风火轮二代（KS-CPU）", "风火轮二代（KS-N卡加速）", "鸭嘴兽（KS）25号最新通道"]:
            # 显示图片相关UI，隐藏B视频相关UI
            self.b_material_label.setText("图片文件夹")
            self.select_image_folder_btn.show()
            self.select_b_video_btn.hide()

            # 如果勾选了AI生成图片，禁用图片文件夹选择按钮
            if self.ai_generate_images_checkbox.isChecked():
                self.select_image_folder_btn.setEnabled(False)
                self.select_image_folder_btn.setText("AI生成模式（无需选择）")
                self.image_folder_label.setText("AI将自动生成二次元图片")
                self.image_folder_label.setStyleSheet("color: #e67e22; font-size: 14px; font-weight: bold;")
                self.image_count_label.setText("图片数量将根据A视频数量自动匹配")
            else:
                self.select_image_folder_btn.setEnabled(True)
                self.select_image_folder_btn.setText("选择图片文件夹")

            # 重置B视频相关状态
            self.b_video_folder_path = ""

        elif function_name == "夏日狂欢（KS）":
            # 显示B视频相关UI，隐藏图片相关UI
            self.b_material_label.setText("B视频文件夹")
            self.select_image_folder_btn.hide()
            self.select_b_video_btn.show()

            # 重置图片相关状态
            self.image_folder_path = ""

        # elif function_name == "鱿鱼游戏（快手优化版）":
        #     # 显示B视频相关UI，隐藏图片相关UI
        #     self.b_material_label.setText("B视频文件夹")
        #     self.select_image_folder_btn.hide()
        #     self.select_b_video_btn.show()
        #
        #     # 重置图片相关状态
        #     self.image_folder_path = ""

        # 更新显示和按钮状态
        self.update_b_material_display()
        self.check_videos_and_update_buttons()

    def on_ai_generate_checkbox_changed(self, state):
        """AI生成图片勾选框状态改变"""
        if state == Qt.Checked:
            # 勾选AI生成时，禁用图片文件夹选择
            self.select_image_folder_btn.setEnabled(False)
            self.select_image_folder_btn.setText("AI生成模式（无需选择）")
            self.image_folder_label.setText("AI将自动生成二次元图片")
            self.image_folder_label.setStyleSheet("color: #e67e22; font-size: 14px; font-weight: bold;")
            self.image_count_label.setText("图片数量将根据A视频数量自动匹配")
        else:
            # 取消勾选时，恢复图片文件夹选择
            self.select_image_folder_btn.setEnabled(True)
            self.select_image_folder_btn.setText("选择图片文件夹")
            self.update_b_material_display()

        self.check_videos_and_update_buttons()

    def select_image_folder(self):
        """选择图片文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
        if folder:
            # 检查文件夹中是否包含图片文件
            image_count = self.count_images_in_folder(folder)
            if image_count == 0:
                QMessageBox.warning(self, "警告",
                    "选择的文件夹中没有找到图片文件！\n\n"
                    "支持的图片格式：.jpg, .jpeg, .png, .bmp, .gif, .tiff, .webp\n"
                    "请选择包含图片文件的文件夹。")
                return

            self.image_folder_path = folder
            self.update_b_material_display()
            self.check_videos_and_update_buttons()

    def select_b_video_folder(self):
        """选择B视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择B视频文件夹")
        if folder:
            self.b_video_folder_path = folder
            self.update_b_material_display()
            self.check_videos_and_update_buttons()

    def update_b_material_display(self):
        """更新B素材显示"""
        if self.current_function in ["星火KS（优化版）", "风火轮（KS爆流）", "风火轮二代（KS-CPU）", "风火轮二代（KS-N卡加速）", "鸭嘴兽（KS）25号最新通道"]:
            # 图片文件夹模式
            if self.image_folder_path and os.path.exists(self.image_folder_path):
                folder_name = os.path.basename(self.image_folder_path)
                self.image_folder_label.setText(f"已选择: {folder_name}")
                self.image_folder_label.setStyleSheet("color: #27ae60; font-size: 14px;")

                # 统计图片数量
                image_count = self.count_images_in_folder(self.image_folder_path)
                self.image_count_label.setText(f"包含 {image_count} 个图片文件")
            else:
                self.image_folder_label.setText("未选择图片文件夹")
                self.image_folder_label.setStyleSheet("color: #7f8c8d; font-size: 14px;")
                self.image_count_label.setText("")

        elif self.current_function == "夏日狂欢（KS）":
            # B视频文件夹模式
            if self.b_video_folder_path and os.path.exists(self.b_video_folder_path):
                folder_name = os.path.basename(self.b_video_folder_path)
                self.image_folder_label.setText(f"已选择: {folder_name}")
                self.image_folder_label.setStyleSheet("color: #27ae60; font-size: 14px;")

                # 统计B视频数量
                video_count = self.count_videos_in_folder(self.b_video_folder_path)
                self.image_count_label.setText(f"包含 {video_count} 个B视频文件")
            else:
                self.image_folder_label.setText("未选择B视频文件夹")
                self.image_folder_label.setStyleSheet("color: #7f8c8d; font-size: 14px;")
                self.image_count_label.setText("")

        # elif self.current_function == "鱿鱼游戏（快手优化版）":
        #     # B视频文件夹模式
        #     if self.b_video_folder_path and os.path.exists(self.b_video_folder_path):
        #         folder_name = os.path.basename(self.b_video_folder_path)
        #         self.image_folder_label.setText(f"已选择: {folder_name}")
        #         self.image_folder_label.setStyleSheet("color: #27ae60; font-size: 14px;")
        #
        #         # 统计B视频数量
        #         video_count = self.count_videos_in_folder(self.b_video_folder_path)
        #         self.image_count_label.setText(f"包含 {video_count} 个B视频文件")
        #     else:
        #         self.image_folder_label.setText("未选择B视频文件夹")
        #         self.image_folder_label.setStyleSheet("color: #7f8c8d; font-size: 14px;")
        #         self.image_count_label.setText("")

    def update_image_folder_display(self):
        """更新图片文件夹显示（保持向后兼容）"""
        self.update_b_material_display()

    def count_images_in_folder(self, folder_path):
        """统计文件夹中的图片数量"""
        if not os.path.exists(folder_path):
            return 0

        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
        count = 0

        try:
            for filename in os.listdir(folder_path):
                if os.path.isfile(os.path.join(folder_path, filename)):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in image_extensions:
                        count += 1
        except Exception:
            pass

        return count

    def count_videos_in_folder(self, folder_path):
        """统计文件夹中的视频数量"""
        if not os.path.exists(folder_path):
            return 0

        video_extensions = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp'}
        count = 0

        try:
            for filename in os.listdir(folder_path):
                if os.path.isfile(os.path.join(folder_path, filename)):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in video_extensions:
                        count += 1
        except Exception:
            pass

        return count

    def is_video_file(self, file_path):
        """检查文件是否为视频文件"""
        if not os.path.isfile(file_path):
            return False

        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp']
        _, ext = os.path.splitext(file_path.lower())
        return ext in video_extensions

    def get_video_info(self, video_path):
        """获取视频信息"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None

            # 获取视频信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            duration = total_frames / fps if fps > 0 else 0

            cap.release()

            return {
                'path': video_path,
                'filename': os.path.basename(video_path),
                'duration': duration,
                'width': width,
                'height': height,
                'fps': fps
            }
        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return None

    def format_duration(self, duration_seconds):
        """格式化时长显示"""
        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)
        seconds = int(duration_seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def select_output_directory(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_path_edit.setText(directory)

    def get_output_directory(self):
        """获取输出目录，如果未设置则返回None"""
        output_dir = self.output_path_edit.text().strip()
        return output_dir if output_dir else None

    def process_yin_yang(self):
        # 夏日狂欢功能无需服务器验证，直接处理
        if self.current_function == "夏日狂欢（KS）":
            self.continue_processing_kuangsha()
        else:
            # 其他功能需要进行服务器验证
            self.verify_feature_code()

    def verify_feature_code(self):
        """验证功能码"""
        try:
            # 禁用处理按钮，显示处理中状态
            self.yin_yang_btn.setEnabled(False)
            self.yin_yang_btn.setText("验证中...")

            # 根据功能选择不同的功能码
            if self.current_function == "风火轮（KS爆流）":
                feature_code = "20250706"  # 风火轮功能码
            elif self.current_function == "风火轮二代（KS-CPU）":
                feature_code = "07150001"  # 风火轮二代功能码
            elif self.current_function == "风火轮二代（KS-N卡加速）":
                feature_code = "07150002"  # 风火轮二代N卡功能码
            elif self.current_function == "鸭嘴兽（KS）25号最新通道":
                feature_code = "20250724"  # 鸭嘴兽功能码
            else:
                feature_code = "MoneyKS"  # 星火KS功能码

            # 准备请求数据
            api_url = "http://47.122.30.31/api/verify.php"
            data = {
                "key": self.card_key,
                "feature_code": feature_code
            }

            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            # 发送验证请求（同步方式）
            print(f"[验证] 发送请求到: {api_url}")
            print(f"[验证] 请求数据: {data}")
            response = requests.post(api_url, json=data, headers=headers, timeout=10)
            response_data = response.json()
            print(f"[验证] 响应数据: {response_data}")

            if response_data.get("status") == True:
                # 获取加密的配置代码
                encrypted_code = response_data.get("data", {}).get("code", "")
                print(f"验证代码(加密): {encrypted_code}")

                # 解密代码
                if encrypted_code and self.aes_key and self.aes_iv:
                    try:
                        aes_key = self.aes_key.encode('utf-8')
                        aes_iv = self.aes_iv.encode('utf-8')

                        cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                        decrypted = unpad(cipher.decrypt(base64.b64decode(encrypted_code)), AES.block_size)
                        self.decrypted_code = decrypted.decode('utf-8').strip()
                        print(f"验证代码(解密): {self.decrypted_code}")

                        # 继续处理
                        self.continue_processing()

                    except Exception as e:
                        print(f"解密验证代码失败: {str(e)}")
                        QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                        self.status_label.setText("验证失败")
                        # 解密失败时恢复按钮状态
                        self.yin_yang_btn.setText("开始处理")
                        self.yin_yang_btn.setEnabled(True)
                else:
                    QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                    self.status_label.setText("验证失败")
                    # 验证失败时恢复按钮状态
                    self.yin_yang_btn.setText("开始处理")
                    self.yin_yang_btn.setEnabled(True)
            else:
                _ = response_data.get("message", "验证失败")
                QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                self.status_label.setText("验证失败")
                # 验证失败时恢复按钮状态
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setEnabled(True)

        except Exception as e:
            print(f"[验证] 验证功能权限失败: {e}")
            import traceback
            print(f"[验证] 详细错误信息: {traceback.format_exc()}")
            QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
            self.status_label.setText("验证失败")
            # 验证失败时恢复按钮状态
            self.yin_yang_btn.setText("开始处理")
            self.yin_yang_btn.setEnabled(True)

    def continue_processing(self):
        """继续处理（验证成功后）"""
        print(f"[处理] 开始继续处理，解密代码: {self.decrypted_code}")
        if not self.decrypted_code:
            print("[处理] 解密代码为空，处理失败")
            QMessageBox.critical(self, "验证失败", "验证失败：请尝试重新启动或联系客服处理。")
            return

        if self.is_processing:
            # 取消处理逻辑
            if hasattr(self, 'current_processor') and self.current_processor.isRunning():
                self.current_processor.is_cancelled = True
                self.current_processor.quit()
                self.current_processor.wait()

            self.is_processing = False
            self.yin_yang_btn.setText("开始处理")
            self.yin_yang_btn.setObjectName("")
            self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
            self.yin_yang_btn.setEnabled(True)

            # 更新状态显示
            self.progress_bar.setValue(0)
            self.status_label.setText("处理已取消")
            self.status_label.setStyleSheet("color: #f39c12; font-size: 12px;")

            # 清理AI生成的临时图片
            if self.ai_generate_images_checkbox.isChecked():
                self.cleanup_ai_images()

            return

        # 获取视频列表
        video_list = []
        for i in range(self.video_list_widget.count()):
            item = self.video_list_widget.item(i)
            video_info = item.data(Qt.UserRole)
            if video_info:
                video_list.append(video_info)

        if not video_list:
            self.status_label.setText("请添加A视频")
            return

        # 根据功能选择进行不同的验证和处理
        if self.current_function == "星火KS（优化版）":
            # 检查是否选择了图片文件夹或AI生成模式
            if not self.ai_generate_images_checkbox.isChecked():
                if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                    self.status_label.setText("请选择图片文件夹或勾选AI生成图片")
                    return
            self.start_xinghuo_processing(video_list)
        elif self.current_function == "风火轮（KS爆流）":
            # 检查是否选择了图片文件夹或AI生成模式
            if not self.ai_generate_images_checkbox.isChecked():
                if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                    self.status_label.setText("请选择图片文件夹或勾选AI生成图片")
                    return
            self.start_fenghuo_processing(video_list)
        elif self.current_function == "风火轮二代（KS-CPU）":
            # 检查是否选择了图片文件夹或AI生成模式
            if not self.ai_generate_images_checkbox.isChecked():
                if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                    self.status_label.setText("请选择图片文件夹或勾选AI生成图片")
                    return
            self.start_fenghuo_v2_processing(video_list)
        elif self.current_function == "风火轮二代（KS-N卡加速）":
            # 检查是否选择了图片文件夹或AI生成模式
            if not self.ai_generate_images_checkbox.isChecked():
                if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                    self.status_label.setText("请选择图片文件夹或勾选AI生成图片")
                    return
            self.start_fenghuo_v2_nvenc_processing(video_list)
        elif self.current_function == "鸭嘴兽（KS）25号最新通道":
            # 检查是否选择了图片文件夹或AI生成模式
            if not self.ai_generate_images_checkbox.isChecked():
                if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                    self.status_label.setText("请选择图片文件夹或勾选AI生成图片")
                    return
            self.start_platypus_processing(video_list)

        # elif self.current_function == "鱿鱼游戏（快手优化版）":
        #     if not self.b_video_folder_path or not os.path.exists(self.b_video_folder_path):
        #         self.status_label.setText("请选择B视频文件夹")
        #         return
        #     self.start_squid_processing(video_list)

    def continue_processing_kuangsha(self):
        """夏日狂欢处理（无需验证）"""
        print("[夏日狂欢] 开始处理，无需服务器验证")

        if self.is_processing:
            # 取消处理逻辑
            if hasattr(self, 'current_processor') and self.current_processor.isRunning():
                self.current_processor.is_cancelled = True
                self.current_processor.quit()
                self.current_processor.wait()

            self.is_processing = False
            self.yin_yang_btn.setText("开始处理")
            self.yin_yang_btn.setObjectName("")
            self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
            self.yin_yang_btn.setEnabled(True)

            # 更新状态显示
            self.progress_bar.setValue(0)
            self.status_label.setText("处理已取消")
            self.status_label.setStyleSheet("color: #f39c12; font-size: 12px;")
            return

        # 获取视频列表
        video_list = []
        for i in range(self.video_list_widget.count()):
            item = self.video_list_widget.item(i)
            video_info = item.data(Qt.UserRole)
            if video_info:
                video_list.append(video_info)

        if not video_list:
            self.status_label.setText("请添加A视频")
            return

        # 检查是否选择了B视频文件夹
        if not self.b_video_folder_path or not os.path.exists(self.b_video_folder_path):
            self.status_label.setText("请选择B视频文件夹")
            return

        # 启动夏日狂欢处理
        self.start_kuangsha_processing(video_list)

    def start_xinghuo_processing(self, video_list):
        """启动星火KS处理"""
        # 设置UI状态
        self.is_processing = True
        self.yin_yang_btn.setText("取消处理")
        self.yin_yang_btn.setObjectName("cancel")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 创建ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()

        # 设置进度条初始值
        self.progress_bar.setValue(0)

        # 处理AI生成图片模式
        image_folder_path = self.image_folder_path
        if self.ai_generate_images_checkbox.isChecked():
            try:
                self.status_label.setText("正在生成AI图片...")
                self.status_label.setStyleSheet("color: #e67e22; font-size: 12px;")

                # 生成与视频数量相等的AI图片
                video_count = len(video_list)
                ai_images = self.generate_ai_images(video_count)

                # 使用AI图片的临时目录作为图片文件夹路径
                image_folder_path = self.ai_generated_temp_dir

                self.status_label.setText(f"AI图片生成完成，共 {len(ai_images)} 张")
                print(f"[星火KS] AI模式：生成了 {len(ai_images)} 张图片，对应 {video_count} 个视频")

            except Exception as e:
                self.status_label.setText(f"AI图片生成失败: {str(e)}")
                self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
                self.is_processing = False
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setObjectName("")
                self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
                self.yin_yang_btn.setEnabled(True)  # 确保按钮可用
                QMessageBox.warning(self, "AI图片生成失败", f"无法生成AI图片：{str(e)}")
                return

        self.status_label.setText("正在准备星火KS处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 创建星火KS处理器
        self.current_processor = FireProcessor(
            ffmpeg_path, video_list, image_folder_path, output_dir, self.decrypted_code
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)
        self.current_processor.used_images_signal.connect(self.on_used_images_received)

        # 显示提示
        QMessageBox.information(self, "星火KS处理", "星火KS处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def start_fenghuo_processing(self, video_list):
        """启动风火轮处理"""
        # 获取ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()
        if not ffmpeg_path:
            self.status_label.setText("找不到FFmpeg")
            return

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 设置UI状态
        self.is_processing = True
        self.yin_yang_btn.setText("取消处理")
        self.yin_yang_btn.setObjectName("cancel")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())

        # 设置进度条初始值
        self.progress_bar.setValue(0)

        # 处理AI生成图片
        image_folder_path = self.image_folder_path
        if self.ai_generate_images_checkbox.isChecked():
            try:
                self.status_label.setText("正在生成AI图片...")
                self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

                # 生成AI图片
                video_count = len(video_list)
                ai_images = self.generate_ai_images(video_count)

                # 使用AI图片的临时目录作为图片文件夹路径
                image_folder_path = self.ai_generated_temp_dir

                self.status_label.setText(f"AI图片生成完成，共 {len(ai_images)} 张")
                print(f"[风火轮] AI模式：生成了 {len(ai_images)} 张图片，对应 {video_count} 个视频")

            except Exception as e:
                self.status_label.setText(f"AI图片生成失败: {str(e)}")
                self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
                self.is_processing = False
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setObjectName("process")
                self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
                self.yin_yang_btn.setEnabled(True)  # 确保按钮可用
                QMessageBox.warning(self, "AI图片生成失败", f"无法生成AI图片：{str(e)}")
                return

        self.status_label.setText("正在准备风火轮处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 验证image_folder_path类型
        if not isinstance(image_folder_path, str):
            error_msg = f"图片文件夹路径类型错误: {type(image_folder_path)}, 值: {image_folder_path}"
            print(f"[风火轮] {error_msg}")
            self.status_label.setText("图片文件夹路径错误")
            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
            QMessageBox.critical(self, "参数错误", error_msg)
            return

        # 创建风火轮处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = FenghuoNonNvencProcessor(
            ffmpeg_path, video_list, image_folder_path, output_dir, self.decrypted_code, delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)
        self.current_processor.used_images_signal.connect(self.on_used_images_received)

        # 显示提示
        QMessageBox.information(self, "风火轮处理", "风火轮处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def start_fenghuo_v2_processing(self, video_list):
        """启动风火轮二代处理"""
        # 获取ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()
        if not ffmpeg_path:
            self.status_label.setText("找不到FFmpeg")
            return

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 获取图片文件夹路径
        image_folder_path = self.image_folder_path

        # 如果勾选了AI生成图片，先生成图片
        if self.ai_generate_images_checkbox.isChecked():
            try:
                self.status_label.setText("正在生成AI图片...")
                self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

                # 生成AI图片
                video_count = len(video_list)
                ai_images = self.generate_ai_images(video_count)

                # 使用AI图片的临时目录作为图片文件夹路径
                image_folder_path = self.ai_generated_temp_dir

                self.status_label.setText(f"AI图片生成完成，共 {len(ai_images)} 张")
                print(f"[风火轮二代] AI模式：生成了 {len(ai_images)} 张图片，对应 {video_count} 个视频")

            except Exception as e:
                self.status_label.setText(f"AI图片生成失败: {str(e)}")
                self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
                self.is_processing = False
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setObjectName("")
                self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
                self.yin_yang_btn.setEnabled(True)
                QMessageBox.warning(self, "AI图片生成失败", f"无法生成AI图片：{str(e)}")
                return

        self.status_label.setText("正在准备风火轮二代处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 验证image_folder_path类型
        if not isinstance(image_folder_path, str):
            error_msg = f"图片文件夹路径类型错误: {type(image_folder_path)}, 值: {image_folder_path}"
            print(f"[风火轮二代] {error_msg}")
            self.status_label.setText("图片文件夹路径错误")
            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
            QMessageBox.critical(self, "参数错误", error_msg)
            return

        # 创建风火轮二代处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = FenghuoV2Processor(
            ffmpeg_path, video_list, image_folder_path, output_dir, self.decrypted_code, delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)
        self.current_processor.used_images_signal.connect(self.on_used_images_received)

        # 显示提示
        QMessageBox.information(self, "风火轮二代处理", "风火轮二代处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def start_platypus_processing(self, video_list):
        """启动鸭嘴兽处理"""
        # 获取ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()
        if not ffmpeg_path:
            self.status_label.setText("找不到FFmpeg")
            return

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 设置UI状态
        self.is_processing = True
        self.yin_yang_btn.setText("取消处理")
        self.yin_yang_btn.setObjectName("cancel")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())

        # 设置进度条初始值
        self.progress_bar.setValue(0)

        # 处理AI生成图片
        image_folder_path = self.image_folder_path
        if self.ai_generate_images_checkbox.isChecked():
            try:
                self.status_label.setText("正在生成AI图片...")
                self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

                # 生成AI图片
                video_count = len(video_list)
                ai_images = self.generate_ai_images(video_count)

                # 使用AI图片的临时目录作为图片文件夹路径
                image_folder_path = self.ai_generated_temp_dir

                self.status_label.setText(f"AI图片生成完成，共 {len(ai_images)} 张")
                print(f"[鸭嘴兽] AI模式：生成了 {len(ai_images)} 张图片，对应 {video_count} 个视频")

            except Exception as e:
                self.status_label.setText(f"AI图片生成失败: {str(e)}")
                self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
                self.is_processing = False
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setObjectName("")
                self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
                self.yin_yang_btn.setEnabled(True)
                QMessageBox.warning(self, "AI图片生成失败", f"无法生成AI图片：{str(e)}")
                return

        self.status_label.setText("正在准备鸭嘴兽处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 验证image_folder_path类型
        if not isinstance(image_folder_path, str):
            error_msg = f"图片文件夹路径类型错误: {type(image_folder_path)}, 值: {image_folder_path}"
            print(f"[鸭嘴兽] {error_msg}")
            self.status_label.setText("图片文件夹路径错误")
            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
            QMessageBox.critical(self, "参数错误", error_msg)
            return

        # 创建鸭嘴兽处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = PlatypusProcessor(
            ffmpeg_path, video_list, image_folder_path, output_dir, self.decrypted_code, delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)
        self.current_processor.used_images_signal.connect(self.on_used_images_received)

        # 显示提示
        QMessageBox.information(self, "鸭嘴兽处理", "鸭嘴兽处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()
        
    def start_fenghuo_v2_nvenc_processing(self, video_list):
        """启动风火轮二代N卡处理"""
        # 获取ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()
        if not ffmpeg_path:
            self.status_label.setText("找不到FFmpeg")
            return

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 获取图片文件夹路径
        image_folder_path = self.image_folder_path

        # 如果勾选了AI生成图片，先生成图片
        if self.ai_generate_images_checkbox.isChecked():
            try:
                self.status_label.setText("正在生成AI图片...")
                self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

                # 生成AI图片
                video_count = len(video_list)
                ai_images = self.generate_ai_images(video_count)

                # 使用AI图片的临时目录作为图片文件夹路径
                image_folder_path = self.ai_generated_temp_dir

                self.status_label.setText(f"AI图片生成完成，共 {len(ai_images)} 张")
                print(f"[风火轮二代N卡] AI模式：生成了 {len(ai_images)} 张图片，对应 {video_count} 个视频")

            except Exception as e:
                self.status_label.setText(f"AI图片生成失败: {str(e)}")
                self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
                self.is_processing = False
                self.yin_yang_btn.setText("开始处理")
                self.yin_yang_btn.setObjectName("")
                self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
                self.yin_yang_btn.setEnabled(True)
                QMessageBox.warning(self, "AI图片生成失败", f"无法生成AI图片：{str(e)}")
                return

        self.status_label.setText("正在准备风火轮二代N卡处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 验证image_folder_path类型
        if not isinstance(image_folder_path, str):
            error_msg = f"图片文件夹路径类型错误: {type(image_folder_path)}, 值: {image_folder_path}"
            print(f"[风火轮二代N卡] {error_msg}")
            self.status_label.setText("图片文件夹路径错误")
            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
            QMessageBox.critical(self, "参数错误", error_msg)
            return

        # 创建风火轮二代N卡处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = FenghuoV2NvencProcessor(
            ffmpeg_path, video_list, image_folder_path, output_dir, self.decrypted_code, delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)
        self.current_processor.used_images_signal.connect(self.on_used_images_received)

        # 显示提示
        QMessageBox.information(self, "风火轮二代N卡处理", "风火轮二代N卡处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def start_squid_processing(self, video_list):
        """启动鱿鱼游戏处理"""
        # 获取B视频列表
        b_video_list = self.get_b_video_list()
        if not b_video_list:
            self.status_label.setText("B视频文件夹中没有找到视频文件")
            return

        # 设置UI状态
        self.is_processing = True
        self.yin_yang_btn.setText("取消处理")
        self.yin_yang_btn.setObjectName("cancel")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 创建ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()

        # 设置进度条初始值
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备鱿鱼游戏处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 创建鱿鱼游戏处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = SquidProcessor(
            ffmpeg_path, video_list, b_video_list, output_dir, self.decrypted_code, delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)

        # 显示提示
        QMessageBox.information(self, "鱿鱼游戏处理", "鱿鱼游戏处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def start_kuangsha_processing(self, video_list):
        """启动夏日狂欢处理"""
        # 获取B视频列表
        b_video_list = self.get_b_video_list()
        if not b_video_list:
            self.status_label.setText("B视频文件夹中没有找到视频文件")
            return

        # 设置UI状态
        self.is_processing = True
        self.yin_yang_btn.setText("取消处理")
        self.yin_yang_btn.setObjectName("cancel")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())

        # 获取输出目录
        output_dir = self.get_output_directory()
        if not output_dir:
            # 如果没有设置输出目录，使用第一个视频所在目录
            output_dir = os.path.dirname(video_list[0]['path'])

        # 创建ffmpeg路径
        ffmpeg_path = self.get_ffmpeg_executable()

        # 设置进度条初始值
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备夏日狂欢处理...")
        self.status_label.setStyleSheet("color: #3498db; font-size: 12px;")

        # 获取A视频路径列表
        a_video_list = [video_info['path'] for video_info in video_list]
        b_video_list_paths = [video_info['path'] for video_info in b_video_list]

        # 创建夏日狂欢处理器
        delete_used_b = self.delete_used_b_checkbox.isChecked()
        self.current_processor = KuangShaProcessor(
            ffmpeg_path, a_video_list, b_video_list_paths, output_dir, "夏日狂欢", delete_used_b
        )

        # 连接信号
        self.current_processor.progress_updated.connect(self.update_progress)
        self.current_processor.process_finished.connect(self.on_processing_finished)

        # 显示提示
        QMessageBox.information(self, "夏日狂欢处理", "夏日狂欢处理任务已准备就绪，点击确定开始处理")

        # 启动处理
        self.current_processor.start()

    def get_b_video_list(self):
        """获取B视频文件夹中的视频列表"""
        if not self.b_video_folder_path or not os.path.exists(self.b_video_folder_path):
            return []

        b_video_list = []
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp']

        try:
            for filename in os.listdir(self.b_video_folder_path):
                file_path = os.path.join(self.b_video_folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in video_extensions:
                        # 获取B视频信息
                        video_info = self.get_video_info(file_path)
                        if video_info:
                            b_video_list.append(video_info)
        except Exception as e:
            print(f"获取B视频列表失败: {e}")

        return b_video_list

    def update_progress(self, value, status):
        """更新处理进度（通用）"""
        self.progress_bar.setValue(value)
        self.status_label.setText(status)

    def update_fire_progress(self, value, status):
        """更新星火KS处理进度（保持向后兼容）"""
        self.update_progress(value, status)

    def on_processing_finished(self, success, message):
        """处理完成（通用）"""
        self.is_processing = False
        self.yin_yang_btn.setText("开始处理")
        self.yin_yang_btn.setObjectName("")
        self.yin_yang_btn.setStyleSheet(self.yin_yang_btn.styleSheet())
        self.yin_yang_btn.setEnabled(True)

        if success:
            self.progress_bar.setValue(100)

            # 根据功能显示不同的完成消息
            if self.current_function == "星火KS（优化版）":
                self.status_label.setText("星火KS处理完成！")
                title = "星火KS处理完成"
            elif self.current_function == "风火轮（KS爆流）":
                self.status_label.setText("风火轮处理完成！")
                title = "风火轮处理完成"
            elif self.current_function == "风火轮二代（KS-CPU）":
                self.status_label.setText("风火轮二代处理完成！")
                title = "风火轮二代处理完成"
            elif self.current_function == "风火轮二代（KS-N卡加速）":
                self.status_label.setText("风火轮二代N卡处理完成！")
                title = "风火轮二代N卡处理完成"
            elif self.current_function == "鸭嘴兽（KS）":
                self.status_label.setText("鸭嘴兽处理完成！")
                title = "鸭嘴兽处理完成"
            # elif self.current_function == "鱿鱼游戏（快手优化版）":
            #     self.status_label.setText("鱿鱼游戏处理完成！")
            #     title = "鱿鱼游戏处理完成"
            else:
                self.status_label.setText("处理完成！")
                title = "处理完成"

            self.status_label.setStyleSheet("color: #27ae60; font-size: 12px;")

            # 如果勾选了删除B素材选项且是星火KS、风火轮或鸭嘴兽功能，则删除使用过的B素材
            if self.delete_used_b_checkbox.isChecked() and self.current_function in ["星火KS（优化版）", "风火轮（KS爆流）", "风火轮二代（KS-CPU）", "风火轮二代（KS-N卡加速）", "鸭嘴兽（KS）"]:
                self.delete_used_b_materials()

            QMessageBox.information(self, title, message)
        else:
            if self.current_function == "星火KS（优化版）":
                self.status_label.setText("星火KS处理失败")
                title = "星火KS处理失败"
            elif self.current_function == "风火轮（KS爆流）":
                self.status_label.setText("风火轮处理失败")
                title = "风火轮处理失败"
            elif self.current_function == "风火轮二代（KS-CPU）":
                self.status_label.setText("风火轮二代处理失败")
                title = "风火轮二代处理失败"
            elif self.current_function == "风火轮二代（KS-N卡加速）":
                self.status_label.setText("风火轮二代N卡处理失败")
                title = "风火轮二代N卡处理失败"
            elif self.current_function == "鸭嘴兽（KS）":
                self.status_label.setText("鸭嘴兽处理失败")
                title = "鸭嘴兽处理失败"
            # elif self.current_function == "鱿鱼游戏（快手优化版）":
            #     self.status_label.setText("鱿鱼游戏处理失败")
            #     title = "鱿鱼游戏处理失败"
            else:
                self.status_label.setText("处理失败")
                title = "处理失败"

            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
            QMessageBox.warning(self, title, message)

        # 清理AI生成的临时图片（无论成功还是失败）
        if self.ai_generate_images_checkbox.isChecked():
            self.cleanup_ai_images()

    def on_fire_finished(self, success, message):
        """星火KS处理完成（保持向后兼容）"""
        self.on_processing_finished(success, message)

    def on_used_images_received(self, used_images):
        """接收实际使用过的图片信息"""
        self.used_b_materials = used_images
        print(f"[主界面] 接收到使用过的图片信息，共 {len(used_images)} 个图片")

    def generate_ai_images(self, count):
        """生成指定数量的AI图片"""
        try:
            # 创建临时目录
            self.ai_generated_temp_dir = tempfile.mkdtemp(prefix="ai_images_")
            print(f"[AI图片] 创建临时目录: {self.ai_generated_temp_dir}")

            # 验证临时目录路径类型
            if not isinstance(self.ai_generated_temp_dir, str):
                raise Exception(f"临时目录路径类型错误: {type(self.ai_generated_temp_dir)}")

            generated_images = []

            for i in range(count):
                try:
                    # 调用API获取图片URL
                    api_url = "https://v2.xxapi.cn/api/randomAcgPic?type=pc&return=json"

                    print(f"[AI图片] 正在获取第 {i+1}/{count} 张图片...")

                    # 发送请求
                    response = requests.get(api_url, timeout=30)
                    response.raise_for_status()

                    # 解析响应
                    data = response.json()
                    if (data.get("code") == "200" or data.get("code") == 200) and data.get("data"):
                        image_url = data["data"]

                        # 下载图片
                        image_filename = f"ai_image_{i+1:03d}.jpg"
                        image_path = os.path.join(self.ai_generated_temp_dir, image_filename)

                        # 下载图片文件
                        urllib.request.urlretrieve(image_url, image_path)
                        generated_images.append(image_path)

                        print(f"[AI图片] 成功下载第 {i+1} 张图片: {image_filename}")

                    else:
                        raise Exception(f"API返回错误: {data}")

                except Exception as e:
                    print(f"[AI图片] 获取第 {i+1} 张图片失败: {str(e)}")
                    # 如果单张图片失败，继续尝试下一张
                    continue

            if len(generated_images) == 0:
                raise Exception("未能成功生成任何AI图片")
            elif len(generated_images) < count:
                print(f"[AI图片] 警告：只成功生成了 {len(generated_images)}/{count} 张图片")

            print(f"[AI图片] 成功生成 {len(generated_images)} 张AI图片")
            return generated_images

        except Exception as e:
            # 清理临时目录
            if self.ai_generated_temp_dir and os.path.exists(self.ai_generated_temp_dir):
                shutil.rmtree(self.ai_generated_temp_dir)
                self.ai_generated_temp_dir = None
            raise Exception(f"AI图片生成失败: {str(e)}")

    def cleanup_ai_images(self):
        """清理AI生成的临时图片"""
        if self.ai_generated_temp_dir and os.path.exists(self.ai_generated_temp_dir):
            try:
                shutil.rmtree(self.ai_generated_temp_dir)
                print(f"[AI图片] 已清理临时目录: {self.ai_generated_temp_dir}")
                self.ai_generated_temp_dir = None
            except Exception as e:
                print(f"[AI图片] 清理临时目录失败: {str(e)}")

    def delete_used_b_materials(self):
        """删除使用过的B素材文件"""
        try:
            if not self.image_folder_path or not os.path.exists(self.image_folder_path):
                return

            # 检查是否有使用过的B素材记录
            if not self.used_b_materials:
                print("没有使用过的B素材记录，跳过删除")
                return

            deleted_count = 0
            error_count = 0

            # 只删除实际使用过的图片文件
            for used_image_path in self.used_b_materials:
                if os.path.exists(used_image_path):
                    try:
                        filename = os.path.basename(used_image_path)
                        os.remove(used_image_path)
                        deleted_count += 1
                        print(f"已删除使用过的B素材: {filename}")
                    except Exception as e:
                        error_count += 1
                        filename = os.path.basename(used_image_path)
                        print(f"删除B素材失败 {filename}: {e}")
                else:
                    print(f"文件不存在，跳过删除: {os.path.basename(used_image_path)}")

            # 显示删除结果
            if deleted_count > 0:
                result_msg = f"已删除 {deleted_count} 个使用过的B素材文件"
                if error_count > 0:
                    result_msg += f"，{error_count} 个文件删除失败"
                print(result_msg)

                # 更新图片文件夹显示
                self.update_image_folder_display()
            else:
                print("没有成功删除任何B素材文件")

            # 清空使用记录
            self.used_b_materials.clear()

        except Exception as e:
            print(f"删除B素材时发生错误: {e}")

    def disable_all_functions(self):
        """禁用所有主界面功能"""
        self.yin_yang_btn.setEnabled(False)

    def check_expiry_time(self):
        """定时检查是否超过到期时间，超过则关闭"""
        if self.expiry_time is None:
            return
        now = datetime.now()
        if now > self.expiry_time:
            self.expiry_check_timer.stop()
            self.disable_all_functions()
            msg = QMessageBox(self)
            msg.setWindowTitle("到期提示")
            msg.setText("您的卡密已到期，请重新登录。")
            msg.setIcon(QMessageBox.Warning)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.show()
            msg.buttonClicked.connect(lambda _: sys.exit(0))
            QTimer.singleShot(3000, lambda: sys.exit(0))

    def update_hot_value(self):
        hot_value = round(random.uniform(100, 888), 1)
        self.notification_label.setText(
            f"🔈：疾风快手版最新爆单排行：{hot_value}万播放量！🎉实时统计中（广场过百万纳入统计数据库）"
        )

    def update_hot_value_with_typing(self):
        """更新热度值并启动打字机效果"""
        hot_value = round(random.uniform(100, 888), 1)
        self.hot_typing_full_text = f"最新爆单排行：{hot_value}万播放量！🎉实时统计中（广场过百万纳入统计数据库）"
        self.hot_typing_index = 0
        self.hot_typing_delay = 0
        self.hot_typing_timer.start(50)  # 每50ms显示一个字符

    def _hot_typing_step(self):
        """打字机效果的单步执行"""
        if self.hot_typing_delay < 20:  # 前1秒延迟
            self.hot_typing_delay += 1
            return

        if self.hot_typing_index < len(self.hot_typing_full_text):
            current_text = self.hot_typing_base + self.hot_typing_full_text[:self.hot_typing_index + 1]
            self.notification_label.setText(current_text)
            self.hot_typing_index += 1
        else:
            self.hot_typing_timer.stop()
            # 显示完成后，设置下次更新
            self.hot_refresh_timer.start(8000)  # 8秒后开始下次更新

    def toggle_bgm(self, state):
        """切换BGM播放状态"""
        if state == 2:  # 选中状态
            self._player.play()
        else:  # 未选中状态
            self._player.pause()





def main():
    app = QApplication(sys.argv)

    # 设置应用程序图标
    app.setWindowIcon(QIcon())

    # 创建登录窗口
    login_win = LoginWindow()
    main_win = None

    def on_login_success(expiry_time, card_key, mute, aes_key, aes_iv):
        nonlocal main_win
        main_win = MainWindow(expiry_time, card_key, mute, aes_key, aes_iv)
        main_win.show()
        QMessageBox.information(main_win, "欢迎使用", f"您的卡密到期时间为：{expiry_time}\n疾风快手版-风火轮助您大展宏图！爆单无数！")
        login_win.close()

    login_win.login_success.connect(on_login_success)
    login_win.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
