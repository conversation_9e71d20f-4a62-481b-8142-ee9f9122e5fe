#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import tempfile
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject

def test_butterfly_processor():
    """测试蝴蝶翻飞处理器"""
    try:
        print("🦋 测试蝴蝶翻飞处理器")
        
        # 导入处理器
        from Codebase_Butterfly import ButterflyProcessor
        
        # 检查必需的信号
        required_signals = ['progress_updated', 'process_finished', 'used_images_signal']
        for signal in required_signals:
            if not hasattr(ButterflyProcessor, signal):
                print(f"❌ 缺少必需信号: {signal}")
                return False
        
        # 检查必需的方法
        required_methods = ['run', 'cancel', 'cleanup_temp_files', 'get_video_info']
        for method in required_methods:
            if not hasattr(ButterflyProcessor, method):
                print(f"❌ 缺少必需方法: {method}")
                return False
        
        # 创建测试实例
        test_a_videos = [{"path": "test_a.mp4", "filename": "test_a.mp4"}]
        test_b_videos = [{"path": "test_b.mp4", "filename": "test_b.mp4"}]
        test_output_dir = tempfile.mkdtemp()
        test_config = {"target_fps": 30, "target_width": 1080, "target_height": 1080}
        
        processor = ButterflyProcessor(
            "ffmpeg.exe", test_a_videos, test_b_videos,
            test_output_dir, json.dumps(test_config), False
        )
        
        # 检查初始化
        if not hasattr(processor, 'config'):
            print("❌ 配置初始化失败")
            return False
        
        if not hasattr(processor, 'temp_dir'):
            print("❌ 临时目录创建失败")
            return False
        
        print("✅ 蝴蝶翻飞处理器类结构正确")
        
        # 清理
        processor.cleanup_temp_files()
        
        return True
        
    except Exception as e:
        print(f"❌ 蝴蝶翻飞处理器测试失败: {e}")
        return False

def test_main_integration():
    """测试主程序集成"""
    try:
        print("\n🔗 测试主程序集成")
        
        # 检查导入是否正常
        import MoneyComehere_v2
        
        # 检查是否有蝴蝶翻飞的导入
        if not hasattr(MoneyComehere_v2, 'ButterflyProcessor'):
            print("❌ 主程序中未找到ButterflyProcessor导入")
            return False
        
        print("✅ 主程序集成正确")
        return True
        
    except Exception as e:
        print(f"❌ 主程序集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🦋 蝴蝶翻飞-视频号处理器测试")
    print("=" * 50)
    
    # 创建QApplication（PyQt5需要）
    app = QApplication(sys.argv)
    
    # 运行测试
    tests = [
        test_butterfly_processor,
        test_main_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！蝴蝶翻飞处理器已成功集成")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
