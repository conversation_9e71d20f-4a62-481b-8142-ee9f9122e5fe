#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import cv2
import numpy as np
import subprocess
import tempfile
import shutil
from PyQt5.QtCore import QThread, pyqtSignal
from datetime import datetime

class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器 - 模拟剪映7步操作流程"""
    
    progress_updated = pyqtSignal(int, str)  # 与主界面兼容的进度信号
    process_finished = pyqtSignal(bool, str)  # 与主界面兼容的完成信号
    used_images_signal = pyqtSignal(list)  # 兼容测试的信号
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_or_function, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None

        # 处理配置参数（兼容字符串和字典）
        if isinstance(config_or_function, str):
            try:
                import json
                self.config = json.loads(config_or_function)
                self.selected_function = "蝴蝶翻飞-视频号"
            except:
                # 如果不是JSON，则作为功能名称处理
                self.selected_function = config_or_function
                self.config = {"target_fps": 30, "target_width": 1080, "target_height": 1080}
        else:
            self.selected_function = "蝴蝶翻飞-视频号"
            self.config = {"target_fps": 30, "target_width": 1080, "target_height": 1080}
        
        # 进度显示相关
        self.current_video_index = 0
        self.total_videos = 0
        self.used_b_videos = set()  # 记录已使用的B视频
        
        # 创建唯一的临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
        print(f"[蝴蝶翻飞] 创建临时目录: {self.temp_dir}")
        
    def get_video_info(self, video_path):
        """获取视频信息（时长、分辨率、帧率）"""
        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            
            # 获取时长
            duration_result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-show_entries", "format=duration",
                 "-of", "default=noprint_wrappers=1:nokey=1", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                timeout=10
            )
            
            # 获取分辨率和帧率
            stream_result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                 "-show_entries", "stream=width,height,r_frame_rate", "-of", "csv=s=,:p=0", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                timeout=10
            )
            
            duration = 0
            width, height, fps = 720, 1280, 30
            
            if duration_result.returncode == 0 and duration_result.stdout.strip():
                duration = float(duration_result.stdout.strip())
            
            if stream_result.returncode == 0 and stream_result.stdout.strip():
                parts = stream_result.stdout.strip().split(',')
                if len(parts) >= 3:
                    width = int(parts[0]) if parts[0] else 720
                    height = int(parts[1]) if parts[1] else 1280
                    # 处理帧率（可能是分数形式）
                    fps_str = parts[2]
                    if '/' in fps_str:
                        num, den = fps_str.split('/')
                        fps = float(num) / float(den) if float(den) != 0 else 30
                    else:
                        fps = float(fps_str) if fps_str else 30
            
            return duration, width, height, fps
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 获取视频信息失败: {e}")
            return 0, 720, 1280, 30
    
    def run_ffmpeg_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            print(f"[蝴蝶翻飞] {step_name} 命令: {' '.join(cmd)}")
            
            self.current_process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            stdout, stderr = self.current_process.communicate()
            
            if self.current_process.returncode != 0:
                print(f"[蝴蝶翻飞] {step_name} 失败: {stderr}")
                return False
            
            print(f"[蝴蝶翻飞] {step_name} 成功完成")
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] {step_name} 异常: {e}")
            return False
        finally:
            self.current_process = None
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"[蝴蝶翻飞] 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"[蝴蝶翻飞] 清理临时文件失败: {e}")
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
                print("[蝴蝶翻飞] 已取消处理")
            except:
                pass
    
    def run(self):
        """主处理流程"""
        try:
            print(f"[蝴蝶翻飞] 开始处理，功能: {self.selected_function}")
            
            # 检查输入
            if not self.a_video_list:
                self.process_finished.emit(False, "没有A视频文件")
                return
            
            if not self.b_video_list:
                self.process_finished.emit(False, "没有B视频文件")
                return
            
            self.total_videos = len(self.a_video_list)
            print(f"[蝴蝶翻飞] 总共需要处理 {self.total_videos} 个A视频")
            print(f"[蝴蝶翻飞] B视频数量: {len(self.b_video_list)} 个")
            
            # 处理每个A视频，B视频循环配对
            success_count = 0
            for i, a_video_path in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                
                self.current_video_index = i + 1
                
                # B视频循环配对
                b_video_path = self.b_video_list[i % len(self.b_video_list)]
                
                if self.process_video_pair(a_video_path, b_video_path, i):
                    success_count += 1
                else:
                    print(f"[蝴蝶翻飞] 第 {i + 1} 个视频处理失败")
            
            # 删除已使用的B视频素材
            if self.delete_used_b and self.used_b_videos:
                self.delete_used_b_videos()
            
            # 完成处理
            if success_count > 0:
                message = f"处理完成！成功处理 {success_count}/{self.total_videos} 个视频"
                if self.delete_used_b and self.used_b_videos:
                    message += f"，已删除 {len(self.used_b_videos)} 个已用B视频素材"
                self.process_finished.emit(True, message)
            else:
                self.process_finished.emit(False, "所有视频处理失败")
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理异常: {e}")
            self.process_finished.emit(False, f"处理过程中发生错误: {str(e)}")
        finally:
            self.cleanup_temp_files()
    
    def process_video_pair(self, a_video_path, b_video_path, pair_index):
        """处理单个A/B视频对 - 实现剪映7步操作流程"""
        try:
            a_filename = os.path.splitext(os.path.basename(a_video_path))[0]
            b_filename = os.path.splitext(os.path.basename(b_video_path))[0]
            
            print(f"[蝴蝶翻飞] 开始处理第 {pair_index + 1} 对视频: {a_filename} + {b_filename}")
            
            # 获取A视频信息（素材视频）
            a_duration, a_width, a_height, a_fps = self.get_video_info(a_video_path)
            if a_duration <= 0:
                print(f"[蝴蝶翻飞] 无法获取A视频时长: {a_video_path}")
                return False
            
            # 获取B视频信息（实拍视频）
            b_duration, b_width, b_height, b_fps = self.get_video_info(b_video_path)
            if b_duration <= 0:
                print(f"[蝴蝶翻飞] 无法获取B视频时长: {b_video_path}")
                return False
            
            print(f"[蝴蝶翻飞] A视频(素材): {a_duration:.2f}秒, {a_width}x{a_height}, {a_fps:.2f}fps")
            print(f"[蝴蝶翻飞] B视频(实拍): {b_duration:.2f}秒, {b_width}x{b_height}, {b_fps:.2f}fps")
            
            # 输出文件路径
            output_filename = f"{a_filename}_sph.mp4"
            final_output = os.path.join(self.output_dir, output_filename)
            
            # 更新进度
            base_progress = int((pair_index / self.total_videos) * 100)
            self.progress_updated.emit(base_progress, f"处理 {a_filename} + {b_filename} ({pair_index + 1}/{self.total_videos})")
            
            # 执行蝴蝶翻飞处理命令（模拟剪映7步操作）
            if not self.execute_butterfly_command(a_video_path, b_video_path, final_output, a_duration):
                return False
            
            # 记录已使用的B视频
            if self.delete_used_b:
                self.used_b_videos.add(b_video_path)
            
            print(f"[蝴蝶翻飞] 成功处理: {output_filename}")
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频对失败: {e}")
            return False

    def execute_butterfly_command(self, a_video_path, b_video_path, output_path, target_duration):
        """执行蝴蝶翻飞处理命令 - 模拟剪映7步操作流程"""
        try:
            self.progress_updated.emit(20, f"步骤1-2: 统一时长和调整比例 ({self.current_video_index}/{self.total_videos})")

            # 临时文件路径
            temp_a_1_1 = os.path.join(self.temp_dir, "temp_a_1_1.mp4")  # A视频调整为1:1
            temp_b_1_1 = os.path.join(self.temp_dir, "temp_b_1_1.mp4")  # B视频调整为1:1并统一时长
            temp_a_audio = os.path.join(self.temp_dir, "temp_a_audio.aac")  # A视频音频
            temp_a_mask = os.path.join(self.temp_dir, "temp_a_mask.mp4")  # A视频添加蒙版
            temp_composite = os.path.join(self.temp_dir, "temp_composite.mp4")  # 复合素材

            # 步骤1-4: 统一时长、调整比例、分离音频、裁剪比例
            # A视频：调整为1:1比例，保持原时长，分离音频
            cmd_a_process = [
                self.ffmpeg_path, "-y",
                "-i", a_video_path,
                # 视频处理：调整为1:1比例（居中裁剪）
                "-vf", "scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080",
                "-c:v", "libx264", "-crf", "23", "-preset", "fast",
                "-an",  # 不包含音频
                temp_a_1_1,
                # 同时提取音频
                "-vn", "-c:a", "aac", "-b:a", "128k",
                temp_a_audio
            ]

            if not self.run_ffmpeg_command(cmd_a_process, "A视频处理(1:1比例+音频分离)"):
                return False

            self.progress_updated.emit(35, f"步骤3-4: B视频时长统一和比例调整 ({self.current_video_index}/{self.total_videos})")

            # B视频：调整时长与A视频一致，调整为1:1比例，静音
            cmd_b_process = [
                self.ffmpeg_path, "-y",
                "-i", b_video_path,
                # 视频处理：调整时长、调整为1:1比例（居中裁剪）
                "-vf", f"scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080",
                "-t", str(target_duration),  # 统一时长
                "-c:v", "libx264", "-crf", "23", "-preset", "fast",
                "-an",  # 静音（关闭实拍音频）
                temp_b_1_1
            ]

            if not self.run_ffmpeg_command(cmd_b_process, "B视频处理(时长统一+1:1比例+静音)"):
                return False

            self.progress_updated.emit(50, f"步骤5: 添加蒙版羽化效果 ({self.current_video_index}/{self.total_videos})")

            # 步骤5: A视频添加蒙版（矩形）羽化参数12
            # 使用boxblur滤镜模拟羽化效果
            cmd_a_mask = [
                self.ffmpeg_path, "-y",
                "-i", temp_a_1_1,
                "-vf", "boxblur=12:1",  # 羽化效果，参数12
                "-c:v", "libx264", "-crf", "23", "-preset", "fast",
                "-an",
                temp_a_mask
            ]

            if not self.run_ffmpeg_command(cmd_a_mask, "A视频添加蒙版羽化"):
                return False

            self.progress_updated.emit(65, f"步骤6: 创建复合素材 ({self.current_video_index}/{self.total_videos})")

            # 步骤6: 创建复合素材（A视频蒙版+B视频叠加）
            # 使用overlay滤镜将A视频（带蒙版）叠加到B视频上
            cmd_composite = [
                self.ffmpeg_path, "-y",
                "-i", temp_b_1_1,  # 底层：B视频
                "-i", temp_a_mask,  # 上层：A视频（带蒙版）
                "-filter_complex", "[1:v]format=yuva420p,colorchannelmixer=aa=0.8[overlay];[0:v][overlay]overlay",
                "-c:v", "libx264", "-crf", "23", "-preset", "fast",
                "-an",
                temp_composite
            ]

            if not self.run_ffmpeg_command(cmd_composite, "创建复合素材"):
                return False

            self.progress_updated.emit(80, f"步骤7: 最终输出9:16比例 ({self.current_video_index}/{self.total_videos})")

            # 步骤7: 调整为9:16比例，缩放高度182-185%（取中间值183.5%），添加音频
            # 计算缩放参数：183.5% = 1.835
            final_height = int(1080 * 1.835)  # 约1981像素
            final_width = int(final_height * 9 / 16)  # 9:16比例，约1114像素

            cmd_final = [
                self.ffmpeg_path, "-y",
                "-i", temp_composite,  # 复合视频
                "-i", temp_a_audio,    # A视频音频
                # 视频处理：调整为9:16比例，缩放高度183.5%
                "-vf", f"scale={final_width}:{final_height},crop=1080:1920:(iw-1080)/2:(ih-1920)/2",
                "-c:v", "libx264", "-crf", "23", "-preset", "fast",
                # 音频处理：使用A视频的音频
                "-c:a", "aac", "-b:a", "128k",
                "-shortest",  # 以最短的流为准
                output_path
            ]

            if not self.run_ffmpeg_command(cmd_final, "最终输出(9:16比例+音频)"):
                return False

            self.progress_updated.emit(95, f"处理完成 ({self.current_video_index}/{self.total_videos})")
            return True

        except Exception as e:
            print(f"[蝴蝶翻飞] 执行命令失败: {e}")
            return False

    def delete_used_b_videos(self):
        """删除已使用的B视频素材"""
        deleted_count = 0
        failed_count = 0

        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"[蝴蝶翻飞] 已删除B视频素材: {b_video_path}")
                else:
                    print(f"[蝴蝶翻飞] B视频素材不存在，跳过: {b_video_path}")
            except Exception as e:
                failed_count += 1
                print(f"[蝴蝶翻飞] 删除B视频素材失败: {b_video_path}, 错误: {e}")

        print(f"[蝴蝶翻飞] B视频素材删除完成: 成功删除 {deleted_count} 个，失败 {failed_count} 个")
