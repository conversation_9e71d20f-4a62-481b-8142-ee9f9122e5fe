#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import os
import cv2
import numpy as np
import tempfile
import shutil
from PyQt5.QtCore import QThread, pyqtSignal
from moviepy.editor import VideoFileClip, CompositeVideoClip, AudioFileClip
import subprocess


class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器"""

    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code=None, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        self.temp_dir = None
        self.used_b_videos = set()

    def cancel_processing(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
            except:
                try:
                    self.current_process.kill()
                except:
                    pass
        print("[蝴蝶翻飞] 处理已取消")

    def run(self):
        """主处理流程"""
        try:
            self.progress_updated.emit(0, "[蝴蝶翻飞] 开始处理...")

            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
            print(f"[蝴蝶翻飞] 临时目录: {self.temp_dir}")

            total_videos = len(self.a_video_list)

            for i, a_video_path in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break

                # 获取对应的B视频
                if i < len(self.b_video_list):
                    b_video_path = self.b_video_list[i]
                else:
                    # 如果B视频不够，循环使用
                    b_video_path = self.b_video_list[i % len(self.b_video_list)]

                progress = int((i / total_videos) * 100)
                self.progress_updated.emit(progress, f"[蝴蝶翻飞] 处理第 {i+1}/{total_videos} 个视频...")

                # 处理单个视频
                success = self.process_single_video(a_video_path, b_video_path, i+1)
                if not success:
                    break

                # 记录已使用的B视频
                self.used_b_videos.add(b_video_path)

            if not self.is_cancelled:
                self.progress_updated.emit(100, "[蝴蝶翻飞] 处理完成!")

                # 删除已使用的B视频（如果启用）
                if self.delete_used_b:
                    self.delete_used_b_videos()

                self.process_finished.emit(True, "处理完成")
            else:
                self.process_finished.emit(False, "处理已取消")

        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            print(f"[蝴蝶翻飞] {error_msg}")
            self.process_finished.emit(False, error_msg)
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    shutil.rmtree(self.temp_dir)
                except:
                    pass

    def process_single_video(self, a_video_path, b_video_path, video_index):
        """处理单个视频对"""
        try:
            print(f"[蝴蝶翻飞] 处理视频对 {video_index}: A={os.path.basename(a_video_path)}, B={os.path.basename(b_video_path)}")

            # 生成输出文件名
            a_name = os.path.splitext(os.path.basename(a_video_path))[0]
            output_path = os.path.join(self.output_dir, f"{a_name}_sph.mp4")

            # 使用moviepy进行视频处理
            success = self.process_with_moviepy(a_video_path, b_video_path, output_path)

            if success:
                print(f"[蝴蝶翻飞] 视频 {video_index} 处理完成: {output_path}")
                return True
            else:
                print(f"[蝴蝶翻飞] 视频 {video_index} 处理失败")
                return False

        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频 {video_index} 时出错: {str(e)}")
            return False

    def process_with_moviepy(self, a_video_path, b_video_path, output_path):
        """使用moviepy处理视频，模拟剪映操作流程"""
        try:
            # 1. 加载素材视频(A)和实拍视频(B)
            print("[蝴蝶翻飞] 加载视频文件...")
            a_clip = VideoFileClip(a_video_path)
            b_clip = VideoFileClip(b_video_path)

            # 2. 统一时长 - 将B视频调整为A视频的时长
            print("[蝴蝶翻飞] 统一视频时长...")
            target_duration = a_clip.duration
            if b_clip.duration != target_duration:
                # 如果B视频更长，裁剪；如果更短，循环
                if b_clip.duration > target_duration:
                    b_clip = b_clip.subclip(0, target_duration)
                else:
                    # 循环B视频直到达到目标时长
                    loops_needed = int(target_duration / b_clip.duration) + 1
                    b_clip = b_clip.loop(loops_needed).subclip(0, target_duration)

            # 3. 音频处理 - 关闭B视频音频，保留A视频音频
            print("[蝴蝶翻飞] 处理音频...")
            b_clip = b_clip.without_audio()
            audio = a_clip.audio

            # 4. 比例调整 - 将两个视频都调整为1:1比例
            print("[蝴蝶翻飞] 调整视频比例为1:1...")
            a_clip_square = self.resize_to_square(a_clip)
            b_clip_square = self.resize_to_square(b_clip)

            # 5. 创建蒙版效果 - 为A视频添加羽化蒙版
            print("[蝴蝶翻飞] 添加蒙版效果...")
            a_clip_masked = self.add_feather_mask(a_clip_square, feather=12)

            # 6. 复合合成
            print("[蝴蝶翻飞] 复合合成...")
            # B视频作为背景，A视频(带蒙版)叠加在上面
            composite = CompositeVideoClip([b_clip_square, a_clip_masked])

            # 7. 调整最终比例为9:16并缩放
            print("[蝴蝶翻飞] 调整最终比例为9:16...")
            final_clip = self.resize_to_9_16(composite, scale_height=1.835)  # 182-185%的中间值

            # 8. 添加音频
            if audio:
                final_clip = final_clip.set_audio(audio)

            # 9. 导出视频
            print(f"[蝴蝶翻飞] 导出视频到: {output_path}")
            final_clip.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(self.temp_dir, 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # 清理资源
            a_clip.close()
            b_clip.close()
            final_clip.close()
            if audio:
                audio.close()

            return True

        except Exception as e:
            print(f"[蝴蝶翻飞] moviepy处理失败: {str(e)}")
            return False

    def resize_to_square(self, clip):
        """将视频调整为1:1正方形比例"""
        w, h = clip.size

        # 计算正方形的边长（取较小的边）
        square_size = min(w, h)

        # 居中裁剪为正方形
        x_center = w // 2
        y_center = h // 2
        x1 = x_center - square_size // 2
        y1 = y_center - square_size // 2
        x2 = x1 + square_size
        y2 = y1 + square_size

        return clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

    def add_feather_mask(self, clip, feather=12):
        """为视频添加羽化蒙版效果"""
        try:
            w, h = clip.size

            # 创建羽化蒙版
            def make_mask(t):
                # 创建一个渐变蒙版
                mask = np.ones((h, w), dtype=np.float32)

                # 添加羽化效果（边缘渐变）
                feather_pixels = int(feather)
                if feather_pixels > 0:
                    # 创建距离变换
                    for y in range(h):
                        for x in range(w):
                            # 计算到边缘的最小距离
                            dist_to_edge = min(x, y, w-1-x, h-1-y)
                            if dist_to_edge < feather_pixels:
                                # 应用羽化
                                alpha = dist_to_edge / feather_pixels
                                mask[y, x] = alpha

                return mask

            # 应用蒙版
            return clip.set_mask(lambda t: make_mask(t))

        except Exception as e:
            print(f"[蝴蝶翻飞] 添加蒙版失败: {str(e)}")
            return clip

    def resize_to_9_16(self, clip, scale_height=1.835):
        """调整视频比例为9:16并缩放"""
        try:
            w, h = clip.size

            # 目标比例 9:16
            target_ratio = 9.0 / 16.0

            # 计算目标尺寸
            target_height = int(h * scale_height)
            target_width = int(target_height * target_ratio)

            # 先缩放
            scaled_clip = clip.resize(height=target_height)

            # 如果宽度不匹配，进行裁剪或填充
            current_w = scaled_clip.size[0]
            if current_w > target_width:
                # 裁剪
                x_offset = (current_w - target_width) // 2
                scaled_clip = scaled_clip.crop(x1=x_offset, x2=x_offset + target_width)
            elif current_w < target_width:
                # 填充（居中）
                scaled_clip = scaled_clip.resize(width=target_width)

            return scaled_clip

        except Exception as e:
            print(f"[蝴蝶翻飞] 调整比例失败: {str(e)}")
            return clip

    def delete_used_b_videos(self):
        """删除已使用的B视频文件"""
        if not self.delete_used_b or not self.used_b_videos:
            return

        deleted_count = 0
        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"[蝴蝶翻飞] 已删除B视频: {os.path.basename(b_video_path)}")
            except Exception as e:
                print(f"[蝴蝶翻飞] 删除B视频失败 {os.path.basename(b_video_path)}: {str(e)}")

        if deleted_count > 0:
            print(f"[蝴蝶翻飞] 共删除 {deleted_count} 个B视频文件")
import numpy as np
import json
import tempfile
import shutil
from PyQt5.QtCore import QThread, pyqtSignal
from datetime import datetime
import subprocess

class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器"""
    
    progress_updated = pyqtSignal(int, str)  # 与主界面兼容的进度信号
    process_finished = pyqtSignal(bool, str)  # 与主界面兼容的完成信号
    used_images_signal = pyqtSignal(set)  # 传递使用过的B视频文件路径集合
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code=None, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path  # 保留以兼容接口，但尽量不使用
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        
        # 进度显示相关
        self.current_video_index = 0
        self.total_videos = 0
        self.used_b_videos = set()
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
        print(f"[蝴蝶翻飞] 创建临时目录: {self.temp_dir}")
        
        # 解析配置
        self.parse_config()
    
    def parse_config(self):
        """解析配置代码"""
        try:
            if isinstance(self.config_code, str):
                self.config = json.loads(self.config_code)
            elif isinstance(self.config_code, dict):
                self.config = self.config_code
            else:
                # 使用默认配置
                self.config = {
                    "target_fps": 30,
                    "target_width": 1080,
                    "target_height": 1080,
                    "final_width": 1080,
                    "final_height": 1920,
                    "scale_height": 183,  # 缩放高度百分比 182-185%
                    "feather_radius": 12,  # 羽化参数
                    "audio_bitrate": "128k",
                    "video_bitrate": "2000k"
                }
            print(f"[蝴蝶翻飞] 配置加载成功: {self.config}")
        except Exception as e:
            print(f"[蝴蝶翻飞] 配置解析失败，使用默认配置: {e}")
            self.config = {
                "target_fps": 30,
                "target_width": 1080,
                "target_height": 1080,
                "final_width": 1080,
                "final_height": 1920,
                "scale_height": 183,
                "feather_radius": 12,
                "audio_bitrate": "128k",
                "video_bitrate": "2000k"
            }
    
    def get_video_info(self, video_path):
        """获取视频信息"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None, None, None, None
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            return duration, width, height, fps
        except Exception as e:
            print(f"[蝴蝶翻飞] 获取视频信息失败: {e}")
            return None, None, None, None
    
    def run(self):
        """主处理流程"""
        try:
            print("[蝴蝶翻飞] 开始处理")
            
            # 检查输入
            if not self.a_video_list:
                self.process_finished.emit(False, "没有A视频文件")
                return
            
            if not self.b_video_list:
                self.process_finished.emit(False, "没有B视频文件")
                return
            
            self.total_videos = len(self.a_video_list)
            print(f"[蝴蝶翻飞] 总共需要处理 {self.total_videos} 个视频")
            
            # 处理每个视频
            success_count = 0
            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                
                self.current_video_index = i + 1
                
                # 选择B视频（循环使用）
                b_video = self.b_video_list[i % len(self.b_video_list)]
                
                if self.process_video_pair(a_video, b_video, i):
                    success_count += 1
                else:
                    print(f"[蝴蝶翻飞] 第 {i + 1} 个视频处理失败")
            
            # 发送使用的B视频信号
            if self.used_b_videos:
                self.used_images_signal.emit(self.used_b_videos)
            
            # 处理完成
            if self.is_cancelled:
                self.process_finished.emit(False, "处理已取消")
            elif success_count == self.total_videos:
                self.process_finished.emit(True, f"蝴蝶翻飞处理完成！成功处理 {success_count} 个视频")
            else:
                self.process_finished.emit(False, f"部分处理失败。成功: {success_count}/{self.total_videos}")
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理异常: {e}")
            self.process_finished.emit(False, f"处理异常: {str(e)}")
        finally:
            self.cleanup_temp_files()
    
    def process_video_pair(self, a_video, b_video, video_index):
        """处理单个视频对"""
        try:
            a_path = a_video['path']
            b_path = b_video['path']
            a_filename = os.path.splitext(a_video['filename'])[0]
            
            print(f"[蝴蝶翻飞] 处理第 {video_index + 1} 个视频: {a_filename}")
            
            # 获取A视频信息
            a_duration, a_width, a_height, a_fps = self.get_video_info(a_path)
            if a_duration is None:
                print(f"[蝴蝶翻飞] 无法获取A视频信息: {a_path}")
                return False
            
            # 获取B视频信息
            b_duration, b_width, b_height, b_fps = self.get_video_info(b_path)
            if b_duration is None:
                print(f"[蝴蝶翻飞] 无法获取B视频信息: {b_path}")
                return False
            
            # 更新进度
            self.progress_updated.emit(5, f"开始处理 {a_filename} ({self.current_video_index}/{self.total_videos})")
            
            # 执行7步处理流程
            output_path = os.path.join(self.output_dir, f"{a_filename}_sph.mp4")
            
            if self.butterfly_process_steps(a_path, b_path, output_path, a_duration, video_index):
                print(f"[蝴蝶翻飞] 成功处理: {a_filename}_sph.mp4")
                
                # 记录已使用的B视频
                if self.delete_used_b:
                    self.used_b_videos.add(b_path)
                
                return True
            else:
                print(f"[蝴蝶翻飞] 处理失败: {a_filename}")
                return False
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频对异常: {e}")
            return False
    
    def butterfly_process_steps(self, a_path, b_path, output_path, duration, video_index):
        """执行蝴蝶翻飞的7步处理流程"""
        try:
            # 步骤1-2: 时长统一和音频处理
            self.progress_updated.emit(15, f"步骤1-2: 时长统一和音频处理 ({self.current_video_index}/{self.total_videos})")
            
            # 步骤3-4: 比例调整为1:1
            self.progress_updated.emit(30, f"步骤3-4: 比例调整为1:1 ({self.current_video_index}/{self.total_videos})")
            
            # 步骤5: 添加蒙版和羽化
            self.progress_updated.emit(50, f"步骤5: 添加蒙版和羽化 ({self.current_video_index}/{self.total_videos})")
            
            # 步骤6: 复合素材处理
            self.progress_updated.emit(70, f"步骤6: 复合素材处理 ({self.current_video_index}/{self.total_videos})")
            
            # 步骤7: 最终导出
            self.progress_updated.emit(90, f"步骤7: 最终导出 ({self.current_video_index}/{self.total_videos})")
            
            # 由于cv2处理复杂视频合成比较困难，这里使用简化的ffmpeg实现
            # 实际项目中可以根据需要进一步优化
            success = self.process_with_ffmpeg_fallback(a_path, b_path, output_path, duration)
            
            if success:
                self.progress_updated.emit(100, f"完成: {os.path.basename(output_path)} ({self.current_video_index}/{self.total_videos})")
                return True
            else:
                return False
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理步骤异常: {e}")
            return False
    
    def process_with_ffmpeg_fallback(self, a_path, b_path, output_path, duration):
        """使用ffmpeg作为后备方案实现蝴蝶翻飞效果"""
        try:
            # 临时文件路径
            temp_a_1x1 = os.path.join(self.temp_dir, f"temp_a_1x1_{self.current_video_index}.mp4")
            temp_b_1x1 = os.path.join(self.temp_dir, f"temp_b_1x1_{self.current_video_index}.mp4")
            temp_b_adjusted = os.path.join(self.temp_dir, f"temp_b_adjusted_{self.current_video_index}.mp4")
            temp_audio = os.path.join(self.temp_dir, f"temp_audio_{self.current_video_index}.aac")
            temp_composite = os.path.join(self.temp_dir, f"temp_composite_{self.current_video_index}.mp4")
            
            # 步骤1: 调整A视频为1:1比例
            cmd1 = [
                self.ffmpeg_path, "-y", "-i", a_path,
                "-vf", f"scale=1080:1080:force_original_aspect_ratio=decrease,pad=1080:1080:(ow-iw)/2:(oh-ih)/2",
                "-t", str(duration), "-an", temp_a_1x1
            ]
            if not self.run_ffmpeg_command(cmd1, "调整A视频比例"):
                return False
            
            # 步骤2: 调整B视频时长和比例为1:1
            cmd2 = [
                self.ffmpeg_path, "-y", "-stream_loop", "-1", "-i", b_path,
                "-vf", f"scale=1080:1080:force_original_aspect_ratio=decrease,pad=1080:1080:(ow-iw)/2:(oh-ih)/2",
                "-t", str(duration), "-an", temp_b_1x1
            ]
            if not self.run_ffmpeg_command(cmd2, "调整B视频"):
                return False
            
            # 步骤3: 提取A视频音频
            cmd3 = [
                self.ffmpeg_path, "-y", "-i", a_path,
                "-vn", "-c:a", "aac", "-b:a", self.config.get("audio_bitrate", "128k"),
                temp_audio
            ]
            if not self.run_ffmpeg_command(cmd3, "提取音频"):
                return False
            
            # 步骤4: 创建蒙版效果和复合处理
            # 使用overlay滤镜实现蒙版效果，并缩放到9:16比例
            filter_complex = (
                f"[0:v]scale=1080:1080,format=yuv420p[a];"
                f"[1:v]scale=1080:1080,format=yuv420p[b];"
                f"[a]boxblur=12:1[a_blur];"  # 羽化效果
                f"[a_blur][b]overlay=0:0:format=auto[comp];"
                f"[comp]scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2[v]"
            )
            
            cmd4 = [
                self.ffmpeg_path, "-y",
                "-i", temp_a_1x1, "-i", temp_b_1x1,
                "-filter_complex", filter_complex,
                "-map", "[v]", "-an", temp_composite
            ]
            if not self.run_ffmpeg_command(cmd4, "复合处理"):
                return False
            
            # 步骤5: 最终合成（添加音频）
            cmd5 = [
                self.ffmpeg_path, "-y",
                "-i", temp_composite, "-i", temp_audio,
                "-map", "0:v", "-map", "1:a",
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "copy", output_path
            ]
            if not self.run_ffmpeg_command(cmd5, "最终合成"):
                return False
            
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] ffmpeg处理异常: {e}")
            return False
    
    def run_ffmpeg_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            print(f"[蝴蝶翻飞] {step_name}: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            self.current_process = process
            stdout, stderr = process.communicate()
            self.current_process = None
            
            if process.returncode != 0:
                print(f"[蝴蝶翻飞] {step_name}失败: {stderr}")
                return False
            
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] {step_name}异常: {e}")
            return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"[蝴蝶翻飞] 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"[蝴蝶翻飞] 清理临时文件失败: {e}")
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
            except:
                pass
