import os
import cv2
import numpy as np
import tempfile
import shutil
import json
from PyQt5.QtCore import QThread, pyqtSignal
from moviepy.editor import VideoFileClip, CompositeVideoClip
import subprocess


class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器"""
    
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code=None, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        self.temp_dir = None
        self.used_b_videos = set()
        
    def cancel_processing(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
            except:
                try:
                    self.current_process.kill()
                except:
                    pass
        print("[蝴蝶翻飞] 处理已取消")
        
    def run(self):
        """主处理流程"""
        try:
            self.progress_updated.emit(0, "[蝴蝶翻飞] 开始处理...")
            
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
            print(f"[蝴蝶翻飞] 临时目录: {self.temp_dir}")
            
            total_videos = len(self.a_video_list)
            
            for i, a_video_path in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                    
                # 获取对应的B视频
                if i < len(self.b_video_list):
                    b_video_path = self.b_video_list[i]
                else:
                    # 如果B视频不够，循环使用
                    b_video_path = self.b_video_list[i % len(self.b_video_list)]
                
                progress = int((i / total_videos) * 100)
                self.progress_updated.emit(progress, f"[蝴蝶翻飞] 处理第 {i+1}/{total_videos} 个视频...")
                
                # 处理单个视频
                success = self.process_single_video(a_video_path, b_video_path, i+1)
                if not success:
                    break
                    
                # 记录已使用的B视频
                self.used_b_videos.add(b_video_path)
            
            if not self.is_cancelled:
                self.progress_updated.emit(100, "[蝴蝶翻飞] 处理完成!")
                
                # 删除已使用的B视频（如果启用）
                if self.delete_used_b:
                    self.delete_used_b_videos()
                
                self.process_finished.emit(True, "处理完成")
            else:
                self.process_finished.emit(False, "处理已取消")
                
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            print(f"[蝴蝶翻飞] {error_msg}")
            self.process_finished.emit(False, error_msg)
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    shutil.rmtree(self.temp_dir)
                except:
                    pass
                    
    def process_single_video(self, a_video_path, b_video_path, video_index):
        """处理单个视频对"""
        try:
            print(f"[蝴蝶翻飞] 处理视频对 {video_index}: A={os.path.basename(a_video_path)}, B={os.path.basename(b_video_path)}")
            
            # 生成输出文件名
            a_name = os.path.splitext(os.path.basename(a_video_path))[0]
            output_path = os.path.join(self.output_dir, f"{a_name}_sph.mp4")
            
            # 使用moviepy进行视频处理
            success = self.process_with_moviepy(a_video_path, b_video_path, output_path)
            
            if success:
                print(f"[蝴蝶翻飞] 视频 {video_index} 处理完成: {output_path}")
                return True
            else:
                print(f"[蝴蝶翻飞] 视频 {video_index} 处理失败")
                return False
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频 {video_index} 时出错: {str(e)}")
            return False
            
    def process_with_moviepy(self, a_video_path, b_video_path, output_path):
        """使用moviepy处理视频，模拟剪映操作流程"""
        try:
            # 1. 加载素材视频(A)和实拍视频(B)
            print("[蝴蝶翻飞] 加载视频文件...")
            a_clip = VideoFileClip(a_video_path)
            b_clip = VideoFileClip(b_video_path)
            
            # 2. 统一时长 - 将B视频调整为A视频的时长
            print("[蝴蝶翻飞] 统一视频时长...")
            target_duration = a_clip.duration
            if b_clip.duration != target_duration:
                # 如果B视频更长，裁剪；如果更短，循环
                if b_clip.duration > target_duration:
                    b_clip = b_clip.subclip(0, target_duration)
                else:
                    # 循环B视频直到达到目标时长
                    loops_needed = int(target_duration / b_clip.duration) + 1
                    b_clip = b_clip.loop(loops_needed).subclip(0, target_duration)
            
            # 3. 音频处理 - 关闭B视频音频，保留A视频音频
            print("[蝴蝶翻飞] 处理音频...")
            b_clip = b_clip.without_audio()
            audio = a_clip.audio
            
            # 4. 比例调整 - 将两个视频都调整为1:1比例
            print("[蝴蝶翻飞] 调整视频比例为1:1...")
            a_clip_square = self.resize_to_square(a_clip)
            b_clip_square = self.resize_to_square(b_clip)
            
            # 5. 创建蒙版效果 - 为A视频添加羽化蒙版
            print("[蝴蝶翻飞] 添加蒙版效果...")
            a_clip_masked = self.add_feather_mask(a_clip_square, feather=12)
            
            # 6. 复合合成
            print("[蝴蝶翻飞] 复合合成...")
            # B视频作为背景，A视频(带蒙版)叠加在上面
            composite = CompositeVideoClip([b_clip_square, a_clip_masked])
            
            # 7. 调整最终比例为9:16并缩放
            print("[蝴蝶翻飞] 调整最终比例为9:16...")
            final_clip = self.resize_to_9_16(composite, scale_height=1.835)  # 182-185%的中间值
            
            # 8. 添加音频
            if audio:
                final_clip = final_clip.set_audio(audio)
            
            # 9. 导出视频
            print(f"[蝴蝶翻飞] 导出视频到: {output_path}")
            final_clip.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(self.temp_dir, 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # 清理资源
            a_clip.close()
            b_clip.close()
            final_clip.close()
            if audio:
                audio.close()
            
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] moviepy处理失败: {str(e)}")
            return False
            
    def resize_to_square(self, clip):
        """将视频调整为1:1正方形比例"""
        w, h = clip.size
        
        # 计算正方形的边长（取较小的边）
        square_size = min(w, h)
        
        # 居中裁剪为正方形
        x_center = w // 2
        y_center = h // 2
        x1 = x_center - square_size // 2
        y1 = y_center - square_size // 2
        x2 = x1 + square_size
        y2 = y1 + square_size
        
        return clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)
        
    def add_feather_mask(self, clip, feather=12):
        """为视频添加羽化蒙版效果"""
        try:
            w, h = clip.size
            
            # 创建静态羽化蒙版（只创建一次，提高效率）
            mask = np.ones((h, w), dtype=np.float32)
            feather_pixels = int(feather)
            
            if feather_pixels > 0:
                # 使用numpy向量化操作提高效率
                y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
                
                # 计算到各边缘的距离
                dist_left = x_coords
                dist_right = w - 1 - x_coords
                dist_top = y_coords
                dist_bottom = h - 1 - y_coords
                
                # 计算到最近边缘的距离
                dist_to_edge = np.minimum.reduce([dist_left, dist_right, dist_top, dist_bottom])
                
                # 应用羽化效果
                feather_mask = np.where(dist_to_edge < feather_pixels, 
                                      dist_to_edge / feather_pixels, 1.0)
                mask = feather_mask.astype(np.float32)
            
            # 应用静态蒙版
            return clip.set_mask(mask)
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 添加蒙版失败: {str(e)}")
            return clip
            
    def resize_to_9_16(self, clip, scale_height=1.835):
        """调整视频比例为9:16并缩放"""
        try:
            w, h = clip.size
            
            # 目标比例 9:16
            target_ratio = 9.0 / 16.0
            
            # 计算目标尺寸
            target_height = int(h * scale_height)
            target_width = int(target_height * target_ratio)
            
            # 先缩放
            scaled_clip = clip.resize(height=target_height)
            
            # 如果宽度不匹配，进行裁剪或填充
            current_w = scaled_clip.size[0]
            if current_w > target_width:
                # 裁剪
                x_offset = (current_w - target_width) // 2
                scaled_clip = scaled_clip.crop(x1=x_offset, x2=x_offset + target_width)
            elif current_w < target_width:
                # 填充（居中）
                scaled_clip = scaled_clip.resize(width=target_width)
            
            return scaled_clip
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 调整比例失败: {str(e)}")
            return clip
            
    def delete_used_b_videos(self):
        """删除已使用的B视频文件"""
        if not self.delete_used_b or not self.used_b_videos:
            return
            
        deleted_count = 0
        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"[蝴蝶翻飞] 已删除B视频: {os.path.basename(b_video_path)}")
            except Exception as e:
                print(f"[蝴蝶翻飞] 删除B视频失败 {os.path.basename(b_video_path)}: {str(e)}")
        
        if deleted_count > 0:
            print(f"[蝴蝶翻飞] 共删除 {deleted_count} 个B视频文件")
