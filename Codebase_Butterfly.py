import os
import subprocess
import json
import tempfile
import shutil
import cv2
import numpy as np
from PyQt5.QtCore import QThread, pyqtSignal


class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器"""
    
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    used_images_signal = pyqtSignal(list)  # 兼容信号
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        self.temp_dir = None
        self.used_b_videos = set()
        
        # 无需服务器验证，使用默认配置
        self.config = {
            "target_fps": 30,
            "target_width": 1080,
            "target_height": 1080,
            "feather_value": 12,
            "final_aspect": "9:16",
            "scale_height": 182  # 缩放高度百分比
        }
        
    def get_video_info(self, video_path):
        """获取视频信息"""
        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            
            # 获取时长
            duration_cmd = [ffprobe_path, "-v", "error", "-show_entries", "format=duration",
                           "-of", "default=noprint_wrappers=1:nokey=1", video_path]
            duration_result = subprocess.run(duration_cmd, stdout=subprocess.PIPE, 
                                           stderr=subprocess.PIPE, text=True,
                                           creationflags=subprocess.CREATE_NO_WINDOW)
            duration = float(duration_result.stdout.strip()) if duration_result.returncode == 0 else 0
            
            # 获取分辨率
            resolution_cmd = [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                             "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", video_path]
            resolution_result = subprocess.run(resolution_cmd, stdout=subprocess.PIPE,
                                             stderr=subprocess.PIPE, text=True,
                                             creationflags=subprocess.CREATE_NO_WINDOW)
            
            width, height = 1920, 1080  # 默认值
            if resolution_result.returncode == 0 and resolution_result.stdout.strip():
                resolution = resolution_result.stdout.strip()
                if 'x' in resolution:
                    width, height = map(int, resolution.split('x'))
            
            return duration, width, height
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 获取视频信息失败: {e}")
            return 0, 1920, 1080
    
    def run_ffmpeg_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            print(f"[蝴蝶翻飞] 执行{step_name}: {' '.join(cmd)}")
            
            self.current_process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            stdout, stderr = self.current_process.communicate()
            
            if self.current_process.returncode != 0:
                print(f"[蝴蝶翻飞] {step_name}失败: {stderr}")
                return False
            
            print(f"[蝴蝶翻飞] {step_name}成功")
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] {step_name}异常: {e}")
            return False
        finally:
            self.current_process = None
    
    def run(self):
        """主处理流程"""
        try:
            print("[蝴蝶翻飞] 开始处理")
            
            # 检查输入
            if not self.a_video_list or not self.b_video_list:
                self.process_finished.emit(False, "视频列表为空")
                return
            
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
            print(f"[蝴蝶翻飞] 创建临时目录: {self.temp_dir}")
            
            total_videos = len(self.a_video_list)
            success_count = 0
            
            # 处理每个A视频与B视频的配对
            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                
                # B视频循环配对
                b_video = self.b_video_list[i % len(self.b_video_list)]
                
                self.progress_updated.emit(
                    int((i / total_videos) * 100),
                    f"正在处理第 {i+1}/{total_videos} 个视频: {os.path.basename(a_video['path'])}"
                )
                
                if self.process_video_pair(a_video, b_video, i):
                    success_count += 1
                    # 记录已使用的B视频
                    if self.delete_used_b:
                        self.used_b_videos.add(b_video['path'])
                else:
                    print(f"[蝴蝶翻飞] 第 {i+1} 个视频处理失败")
            
            # 删除已使用的B视频素材
            if self.delete_used_b and self.used_b_videos:
                self.progress_updated.emit(95, "正在删除已用B视频素材...")
                self.delete_used_b_videos()
            
            self.progress_updated.emit(100, "处理完成")
            
            if success_count == total_videos:
                self.process_finished.emit(True, f"成功处理了 {success_count} 个视频")
            else:
                self.process_finished.emit(False, f"处理完成，成功 {success_count}/{total_videos} 个视频")
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理过程中发生错误: {e}")
            self.process_finished.emit(False, f"处理失败: {str(e)}")
        finally:
            self.cleanup_temp_files()
    
    def process_video_pair(self, a_video, b_video, pair_index):
        """处理单个视频对 - 六步流程"""
        try:
            a_path = a_video['path']
            b_path = b_video['path']
            a_filename = os.path.splitext(os.path.basename(a_path))[0]
            
            print(f"[蝴蝶翻飞] 处理视频对 {pair_index+1}: {a_filename}")
            
            # 获取A视频信息
            duration, width, height = self.get_video_info(a_path)
            
            if duration <= 0:
                print(f"[蝴蝶翻飞] 无法获取A视频时长: {a_path}")
                return False
            
            print(f"[蝴蝶翻飞] A视频信息: {width}x{height}, 时长: {duration}秒")
            
            # 定义临时文件路径
            temp_audio = os.path.join(self.temp_dir, f"audio_{pair_index}.wav")
            temp_b_1_1 = os.path.join(self.temp_dir, f"b_1_1_{pair_index}.mp4")
            temp_overlay = os.path.join(self.temp_dir, f"overlay_{pair_index}.mp4")
            temp_9_16 = os.path.join(self.temp_dir, f"temp_9_16_{pair_index}.mp4")
            tempa_path = os.path.join(self.temp_dir, f"tempa_{pair_index}.mp4")
            output_path = os.path.join(self.output_dir, f"{a_filename}_JFsph.mp4")
            
            # 六步处理流程
            if not self.step1_extract_audio(a_path, temp_audio):
                return False
            
            if not self.step2_adjust_b_to_1_1(b_path, temp_b_1_1, duration):
                return False
            
            if not self.step3_overlay_with_feather(a_path, temp_b_1_1, temp_overlay, duration):
                return False
            
            if not self.step4_convert_to_9_16(temp_overlay, temp_9_16):
                return False
            
            if not self.step5_export_tempa(temp_9_16, tempa_path):
                return False
            
            if not self.step6_final_process(tempa_path, output_path):
                return False
            
            print(f"[蝴蝶翻飞] 视频对 {pair_index+1} 处理完成: {output_path}")
            return True

        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频对 {pair_index+1} 失败: {e}")
            return False

    def step1_extract_audio(self, a_path, temp_audio):
        """步骤1: 提取A视频的音频"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", a_path,
            "-vn",  # 不要视频流
            "-acodec", "pcm_s16le",  # 使用PCM编码
            "-ar", "44100",  # 采样率
            "-ac", "2",  # 双声道
            temp_audio
        ]

        return self.run_ffmpeg_command(cmd, "步骤1: 提取A视频音频")

    def step2_adjust_b_to_1_1(self, b_path, temp_b_1_1, duration):
        """步骤2: 将B视频调整为1:1比例"""
        target_size = self.config["target_width"]  # 1080x1080

        cmd = [
            self.ffmpeg_path, "-y",
            "-i", b_path,
            "-t", str(duration),  # 限制时长与A视频一致
            "-vf", f"scale={target_size}:{target_size}:force_original_aspect_ratio=decrease,pad={target_size}:{target_size}:(ow-iw)/2:(oh-ih)/2:black",
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23",
            "-r", str(self.config["target_fps"]),
            "-an",  # 移除音频
            temp_b_1_1
        ]

        return self.run_ffmpeg_command(cmd, "步骤2: 调整B视频为1:1比例")

    def step3_overlay_with_feather(self, a_path, temp_b_1_1, temp_overlay, duration):
        """步骤3: 将A视频叠加到1:1的B视频上，边缘羽化"""
        target_size = self.config["target_width"]  # 1080
        feather = self.config["feather_value"]  # 12

        # 简化的叠加方式，确保A视频完整呈现在B视频上，并保持音频
        filter_complex = f"[0:v]scale=-1:{target_size}:force_original_aspect_ratio=decrease[a_scaled];[a_scaled]pad={target_size}:{target_size}:(ow-iw)/2:(oh-ih)/2:black[a_padded];[1:v][a_padded]overlay=0:0[v]"

        cmd = [
            self.ffmpeg_path, "-y",
            "-i", a_path,
            "-i", temp_b_1_1,
            "-filter_complex", filter_complex,
            "-map", "[v]",
            "-map", "0:a?",  # 保留A视频的音频
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23",
            "-r", str(self.config["target_fps"]),
            "-t", str(duration),
            "-c:a", "aac",  # 音频编码
            temp_overlay
        ]

        return self.run_ffmpeg_command(cmd, "步骤3: A视频叠加到B视频(羽化)")

    def step4_convert_to_9_16(self, temp_overlay, temp_9_16):
        """步骤4: 将视频变更为9:16比例，拉伸铺满"""
        # 9:16 比例，以1080为宽度，高度为1920
        target_width = 1080
        target_height = 1920

        cmd = [
            self.ffmpeg_path, "-y",
            "-i", temp_overlay,
            "-vf", f"scale={target_width}:{target_height}:force_original_aspect_ratio=fill",
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23",
            "-r", str(self.config["target_fps"]),
            "-c:a", "aac",  # 保留音频
            temp_9_16
        ]

        return self.run_ffmpeg_command(cmd, "步骤4: 转换为9:16比例")

    def step5_export_tempa(self, temp_9_16, tempa_path):
        """步骤5: 导出为tempa.mp4，1080P，H.264编码，30fps"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", temp_9_16,
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23",
            "-r", "30",  # 固定30fps
            "-s", "1080x1920",  # 固定分辨率
            "-pix_fmt", "yuv420p",
            "-c:a", "aac",  # 保留音频
            tempa_path
        ]

        return self.run_ffmpeg_command(cmd, "步骤5: 导出tempa.mp4")

    def step6_final_process(self, tempa_path, output_path):
        """步骤6: 最终处理 - ffmpeg -i tempa.mp4 -aspect 1:1 output.mp4"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", tempa_path,
            "-aspect", "1:1",
            "-c", "copy",  # 复制流，不重新编码
            output_path
        ]

        return self.run_ffmpeg_command(cmd, "步骤6: 最终处理")

    def delete_used_b_videos(self):
        """删除已使用的B视频素材"""
        deleted_count = 0
        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"[蝴蝶翻飞] 已删除B视频素材: {b_video_path}")
            except Exception as e:
                print(f"[蝴蝶翻飞] 删除B视频素材失败: {b_video_path}, 错误: {e}")

        print(f"[蝴蝶翻飞] B视频素材删除完成: 成功删除 {deleted_count} 个")

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"[蝴蝶翻飞] 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"[蝴蝶翻飞] 清理临时文件失败: {e}")

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
            except:
                pass
        print("[蝴蝶翻飞] 处理已取消")
